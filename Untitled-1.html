<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Primary Source UI</title>
<style>
    body {
        background-color: #1e1e1e;
        font-family: 'Fira Code', monospace;
        color: #ccc;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
    }

    .card {
        background-color: #252526;
        border-radius: 10px;
        padding: 15px;
        width: 420px;
        box-shadow: 0 0 10px rgba(0,0,0,0.3);
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #333;
        padding-bottom: 5px;
        margin-bottom: 15px;
    }

    .title {
        display: flex;
        align-items: center;
        gap: 6px;
        font-weight: bold;
        color: #ccc;
    }

    .title svg {
        cursor: pointer;
        width: 24px;
        height: 24px;
        fill: silver;
        transition: fill 0.2s ease-in-out;
    }

    .status-time {
        font-size: 0.85rem;
        color: #66c2a5;
    }

    .label {
        color: #888;
        margin-bottom: 2px;
        font-size: 0.9rem;
    }

    .value {
        color: #66c2a5;
        font-size: 0.95rem;
        margin-bottom: 10px;
    }

    .value-main {
        color: #ccc;
        font-size: 1rem;
        margin-bottom: 10px;
    }

    select {
        width: 100%;
        padding: 8px;
        background-color: #1e1e1e;
        color: #ccc;
        border: 1px solid #444;
        border-radius: 5px;
        font-family: 'Fira Code', monospace;
    }

    select:disabled {
        background-color: #2a2a2a;
        color: #666;
        border-color: #333;
        cursor: not-allowed;
    }

    .switch-btn {
        background-color: #3a8dde;
        color: white;
        padding: 6px 14px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-family: 'Fira Code', monospace;
        transition: background-color 0.2s;
    }

    .switch-btn:hover:not(:disabled) {
        background-color: #2d77c7;
    }

    .switch-btn:disabled {
        background-color: #2a2a2a;
        color: #666;
        cursor: not-allowed;
    }

    .flex-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }
</style>
</head>
<body>

<div class="card">
    <div class="card-header">
        <div class="title">
            <svg id="lockIcon" viewBox="0 0 24 24">
                <path d="M12 17a2 2 0 100-4 2 2 0 000 4zm6-7h-1V7a5 5 0 00-10 0v3H6a2 2 0 00-2 2v8a2 2 0 002 
                2h12a2 2 0 002-2v-8a2 2 0 00-2-2zm-8-3a3 3 0 016 0v3H10V7z"/>
            </svg>
            Primary Source
        </div>
        <div class="status-time">Active <span id="time">04:28:46 PM</span></div>
    </div>

    <div>
        <div class="label">Active Stream:</div>
        <div class="value">MATCH-1-MAIN-WF-EU-CONT-au-prod-az1</div>

        <div class="label">Current Active Source:</div>
        <div class="flex-row">
            <div class="value-main" id="currentSource">Main</div>
            <button id="switchBtn" class="switch-btn">Switch Now</button>
        </div>

        <div class="label">Source Selection:</div>
        <select id="sourceSelect">
            <option>Main</option>
            <option>Backup</option>
            <option>Other</option>
        </select>

        <div class="label" style="margin-top: 10px;">Output Stream:</div>
        <div class="value">Main-Output-TQ-GV-PROC</div>
    </div>
</div>

<script>
    const lockIcon = document.getElementById('lockIcon');
    const switchBtn = document.getElementById('switchBtn');
    const sourceSelect = document.getElementById('sourceSelect');

    let locked = true;

const lockSVG = `
<svg viewBox="0 0 24 24" fill="silver">
  <path transform="scale(0.95) translate(0.6,0.3)" d="M12 17a2 2 0 100-4 2 2 0 000 4zm6-7h-1V7a5 5 0 00-10 0v3H6a2 2 0 00-2 2v8a2 2 0 002 
  2h12a2 2 0 002-2v-8a2 2 0 00-2-2zm-8-3a3 3 0 016 0v3H10V7z"/>
</svg>`;

const unlockSVG = `
<svg viewBox="0 0 24 24" fill="silver">
  <path transform="scale(0.95) translate(0.6,-0.5)" d="M17 8V7a5 5 0 00-9.9-1h2.1a3 3 0 015.8 1v1H8a2 2 0 00-2 2v8a2 2 0 002 
  2h8a2 2 0 002-2v-8a2 2 0 00-2-2h-1z"/>
</svg>`;


    lockIcon.addEventListener('click', () => {
        locked = !locked;
        lockIcon.innerHTML = locked ? lockSVG : unlockSVG;
        switchBtn.disabled = locked;
        sourceSelect.disabled = locked;
    });

    // Set initial state locked
    lockIcon.innerHTML = lockSVG;
    switchBtn.disabled = true;
    sourceSelect.disabled = true;

    // Update time every second
    function updateTime() {
        const now = new Date();
        document.getElementById('time').textContent = now.toLocaleTimeString();
    }
    setInterval(updateTime, 1000);
    updateTime();

    // Switch Now functionality
    switchBtn.addEventListener('click', () => {
        const selected = sourceSelect.value;
        document.getElementById('currentSource').textContent = selected;
    });
</script>

</body>
</html>
