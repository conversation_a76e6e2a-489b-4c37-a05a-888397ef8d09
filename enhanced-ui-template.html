<style>
    body {
        background-color: #1e1e1e;
        font-family: 'Fira Code', monospace;
        color: #ccc;
        margin: 0;
        padding: 10px;
    }

    .card {
        background-color: #252526;
        border-radius: 10px;
        padding: 15px;
        width: 100%;
        box-shadow: 0 0 10px rgba(0,0,0,0.3);
        box-sizing: border-box;
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #333;
        padding-bottom: 5px;
        margin-bottom: 15px;
    }

    .title {
        display: flex;
        align-items: center;
        gap: 6px;
        font-weight: bold;
        color: #ccc;
    }

    .title svg {
        cursor: pointer;
        width: 24px;
        height: 24px;
        fill: silver;
        transition: fill 0.2s ease-in-out;
    }

    .status-time {
        font-size: 0.85rem;
        color: #66c2a5;
    }

    .label {
        color: #888;
        margin-bottom: 2px;
        font-size: 0.9rem;
    }

    .value {
        color: #66c2a5;
        font-size: 0.95rem;
        margin-bottom: 10px;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .value-main {
        color: #ccc;
        font-size: 1rem;
        margin-bottom: 10px;
        word-wrap: break-word;
        overflow-wrap: break-word;
        flex: 1;
    }

    .source-buttons {
        display: flex;
        gap: 5px;
        margin-bottom: 10px;
        flex-wrap: wrap;
    }

    .source-btn {
        background-color: #3a8dde;
        color: white;
        padding: 6px 14px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-family: 'Fira Code', monospace;
        transition: background-color 0.2s;
        flex: 1;
        min-width: 80px;
    }

    .source-btn:hover:not(:disabled) {
        background-color: #2d77c7;
    }

    .source-btn:disabled {
        background-color: #2a2a2a;
        color: #666;
        cursor: not-allowed;
    }

    .source-btn.active {
        background-color: #66c2a5;
        color: #1e1e1e;
    }

    .switch-btn {
        background-color: #3a8dde;
        color: white;
        padding: 6px 14px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-family: 'Fira Code', monospace;
        transition: background-color 0.2s;
        min-width: 100px;
        position: relative;
    }

    .switch-btn:hover:not(:disabled) {
        background-color: #2d77c7;
    }

    .switch-btn:disabled {
        background-color: #2a2a2a;
        color: #666;
        cursor: not-allowed;
    }

    .switch-btn.loading {
        background-color: #f59e0b;
        cursor: not-allowed;
    }

    .spinner {
        display: inline-block;
        width: 12px;
        height: 12px;
        border: 2px solid #ffffff33;
        border-radius: 50%;
        border-top-color: #ffffff;
        animation: spin 1s ease-in-out infinite;
        margin-right: 5px;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    select {
        width: 100%;
        padding: 8px;
        background-color: #1e1e1e;
        color: #ccc;
        border: 1px solid #444;
        border-radius: 5px;
        font-family: 'Fira Code', monospace;
    }

    select:disabled {
        background-color: #2a2a2a;
        color: #666;
        border-color: #333;
        cursor: not-allowed;
    }

    .flex-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        gap: 10px;
    }

    .online-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
    }

    .online-indicator.online {
        background-color: #66c2a5;
    }

    .online-indicator.offline {
        background-color: #ef4444;
    }
</style>

<div class="card">
    <div class="card-header">
        <div class="title">
            <svg id="lockIcon" viewBox="0 0 24 24">
                <path d="M12 17a2 2 0 100-4 2 2 0 000 4zm6-7h-1V7a5 5 0 00-10 0v3H6a2 2 0 00-2 2v8a2 2 0 002 
                2h12a2 2 0 002-2v-8a2 2 0 00-2-2zm-8-3a3 3 0 016 0v3H10V7z"/>
            </svg>
            <span id="edgeName">Dynamic Source Control</span>
        </div>
        <div class="status-time">
            <span class="online-indicator" id="onlineIndicator"></span>
            <span id="lastUpdated">Last Updated: --:--:--</span>
        </div>
    </div>

    <div>
        <div class="label">Active Stream:</div>
        <div class="value" id="activeStream">--</div>

        <div class="label">Current Active Source:</div>
        <div class="flex-row">
            <div class="value-main" id="currentSource">--</div>
            <button class="switch-btn" id="switchNowBtn" disabled>
                <span id="switchBtnText">Switch Now</span>
            </button>
        </div>

        <div class="label">Source Selection:</div>
        <div class="source-buttons" id="sourceButtons">
            <!-- Dynamic buttons will be generated here -->
        </div>

        <div class="label">Source Dropdown:</div>
        <select id="sourceSelect" disabled>
            <option>Loading...</option>
        </select>

        <div class="label" style="margin-top: 10px;">Output Stream:</div>
        <div class="value" id="outputStream">--</div>
    </div>
</div>

<script>
(function(scope) {
    const lockIcon = document.getElementById('lockIcon');
    const sourceButtons = document.getElementById('sourceButtons');
    const sourceSelect = document.getElementById('sourceSelect');
    const edgeName = document.getElementById('edgeName');
    const activeStream = document.getElementById('activeStream');
    const currentSource = document.getElementById('currentSource');
    const outputStream = document.getElementById('outputStream');
    const onlineIndicator = document.getElementById('onlineIndicator');
    const lastUpdated = document.getElementById('lastUpdated');
    const switchNowBtn = document.getElementById('switchNowBtn');
    const switchBtnText = document.getElementById('switchBtnText');

    let locked = true;
    let currentConfig = null;
    let currentState = null;
    let availableSources = [];
    let currentActiveIndex = -1;
    let isLoading = false;

    const lockSVG = `<path d="M12 17a2 2 0 100-4 2 2 0 000 4zm6-7h-1V7a5 5 0 00-10 0v3H6a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2v-8a2 2 0 00-2-2zm-8-3a3 3 0 016 0v3H10V7z"/>`;
    const unlockSVG = `<path d="M17 8V7a5 5 0 00-9.9-1h2.1a3 3 0 015.8 1v1H8a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-8a2 2 0 00-2-2h-1z"/>`;

    function toggleLock() {
        locked = !locked;
        lockIcon.innerHTML = locked ? lockSVG : unlockSVG;
        updateButtonStates();
    }

    function updateButtonStates() {
        const buttons = sourceButtons.querySelectorAll('.source-btn');
        buttons.forEach(btn => {
            btn.disabled = locked || isLoading;
        });
        sourceSelect.disabled = locked || isLoading;
        switchNowBtn.disabled = locked || isLoading || availableSources.length === 0;
    }

    function setLoadingState(loading) {
        isLoading = loading;
        switchNowBtn.classList.toggle('loading', loading);

        if (loading) {
            switchBtnText.innerHTML = '<span class="spinner"></span>Switching...';
        } else {
            switchBtnText.textContent = 'Switch Now';
        }

        updateButtonStates();
    }

    function createSourceButtons(sources) {
        availableSources = sources || [];
        sourceButtons.innerHTML = '';
        sourceSelect.innerHTML = '';

        // Add default option
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = 'Select source...';
        sourceSelect.appendChild(defaultOption);

        availableSources.forEach((source, index) => {
            // Create button for source pre-selection
            const button = document.createElement('button');
            button.className = 'source-btn';
            button.textContent = source.type || `Source ${index + 1}`;
            button.disabled = locked || isLoading;
            button.addEventListener('click', () => {
                if (!locked && !isLoading) {
                    selectSource(source.type.toLowerCase());
                }
            });
            sourceButtons.appendChild(button);

            // Create option for dropdown
            const option = document.createElement('option');
            option.value = source.type.toLowerCase();
            option.textContent = source.type;
            sourceSelect.appendChild(option);
        });

        updateButtonStates();
    }

    function updateActiveSource(activeSourceType, sourceData) {
        // Update button states
        const buttons = sourceButtons.querySelectorAll('.source-btn');
        buttons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.textContent.toLowerCase() === activeSourceType) {
                btn.classList.add('active');
            }
        });

        // Update dropdown
        sourceSelect.value = activeSourceType || '';

        // Display actual source name from API response
        if (sourceData && sourceData.configuredSources) {
            const activeSource = sourceData.configuredSources.find(s =>
                s.type.toLowerCase() === activeSourceType
            );
            if (activeSource) {
                currentSource.textContent = activeSource.name || activeSource.type;
                // Update current active index for cycling
                currentActiveIndex = sourceData.configuredSources.indexOf(activeSource);
            } else {
                currentSource.textContent = activeSourceType ?
                    activeSourceType.charAt(0).toUpperCase() + activeSourceType.slice(1) : '--';
            }
        } else {
            currentSource.textContent = activeSourceType ?
                activeSourceType.charAt(0).toUpperCase() + activeSourceType.slice(1) : '--';
        }
    }

    function switchToSpecificSource(sourceType) {
        if (locked || isLoading) return;

        // Send message back to Node-RED for specific source
        scope.send({
            currentSource: sourceType,
            action: 'switch',
            switchType: 'specific',
            timestamp: new Date().toISOString()
        });
    }

    function selectSource(sourceType) {
        selectedSourceType = sourceType;

        // Update button visual states
        const buttons = sourceButtons.querySelectorAll('.source-btn');
        buttons.forEach(btn => {
            btn.classList.remove('selected');
            if (btn.textContent.toLowerCase() === sourceType) {
                btn.classList.add('selected');
            }
        });

        // Update dropdown
        sourceSelect.value = sourceType;

        // Update Switch Now button state
        updateButtonStates();
    }

    function switchToSelectedSource() {
        if (locked || isLoading || !selectedSourceType) return;

        setLoadingState(true);

        // Send message back to Node-RED for the pre-selected source
        scope.send({
            currentSource: selectedSourceType,
            action: 'switch',
            switchType: 'preselected',
            timestamp: new Date().toISOString()
        });
    }

    function updateLastUpdatedTime() {
        const now = new Date();
        lastUpdated.textContent = `Last Updated: ${now.toLocaleTimeString()}`;
    }

    function triggerRefresh() {
        // Send message to trigger immediate refresh of the main flow
        scope.send({
            action: 'refresh',
            timestamp: new Date().toISOString()
        });
    }

    // Initialize
    lockIcon.addEventListener('click', toggleLock);
    lockIcon.innerHTML = lockSVG;

    // Switch Now button event listener
    switchNowBtn.addEventListener('click', switchToSelectedSource);

    // Handle dropdown changes for source pre-selection
    sourceSelect.addEventListener('change', (e) => {
        if (!locked && !isLoading && e.target.value) {
            selectSource(e.target.value);
        }
    });

    // Watch for incoming messages from Node-RED
    scope.$watch('msg', function(msg) {
        if (!msg || !msg.payload) return;

        const payload = msg.payload;

        // Handle switch response messages
        if (msg.topic === 'success' && payload.success) {
            // Switch was successful - clear loading state and trigger refresh
            setLoadingState(false);
            updateLastUpdatedTime();

            // Trigger immediate refresh after successful switch
            setTimeout(() => {
                triggerRefresh();
            }, 500);

            return;
        }

        if (msg.topic === 'error' && !payload.success) {
            // Switch failed - clear loading state
            setLoadingState(false);
            return;
        }

        // Update configuration if available
        if (payload.edgeConfig) {
            currentConfig = payload.edgeConfig;
            edgeName.textContent = currentConfig.edgeName || 'Dynamic Source Control';
        }

        // Update UI elements with real API data
        if (payload.configuredStreams && payload.configuredStreams.length > 0) {
            activeStream.textContent = payload.configuredStreams[0].name;
        }

        if (payload.configuredOutputs && payload.configuredOutputs.length > 0) {
            outputStream.textContent = payload.configuredOutputs[0].name;
        }

        if (payload.configuredSources) {
            createSourceButtons(payload.configuredSources);
        }

        // Update online status
        if (typeof payload.online === 'boolean') {
            onlineIndicator.className = `online-indicator ${payload.online ? 'online' : 'offline'}`;
        }

        // Update active source with real source name
        if (msg.currentSource) {
            updateActiveSource(msg.currentSource, payload);
        }

        // Update last updated timestamp
        updateLastUpdatedTime();

        // Clear loading state if this is a regular status update
        if (!msg.topic || msg.topic === 'sync') {
            setLoadingState(false);
        }

        currentState = payload;
    });

})(scope);
</script>
