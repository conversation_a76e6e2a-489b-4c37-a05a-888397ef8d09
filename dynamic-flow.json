[{"id": "77bf3dd067e362fd", "type": "tab", "label": "Dynamic Source Flow", "disabled": false, "info": "", "env": []}, {"id": "config_node_001", "type": "function", "z": "77bf3dd067e362fd", "name": "Configuration Store", "func": "// Dynamic Edge Configuration\nconst edgeConfig = {\n    \"edgeId\": \"6885dadf539d77930a6e3f8a\",\n    \"edgeName\": \"MATCH-1-BACKUP-WF-eu-cont-az2\",\n    \"sources\": [\n        {\n            \"sourceId\": \"2fb6f4fca6f9af1b48b35d8adc835002bcec7679cfbe6b7f\",\n            \"sourceName\": \"MATCH-1-BACKUP-WF-SPL-eu-cont-az2-Input A\",\n            \"type\": \"Main\"\n        },\n        {\n            \"sourceId\": \"93d4edffa63c83a188312c1e267286b0078e9ad1fa35f6ac\",\n            \"sourceName\": \"MATCH-1-BACKUP-WF-eu-cont-az2-Input Caller\",\n            \"type\": \"Backup\"\n        }\n    ],\n    \"outputs\": [\n        {\n            \"outputId\": \"d045eb67e21e173a49622e96b8af30deac7cfc147d11a9c3\",\n            \"outputName\": \"MATCH-1-BACKUP-WF-SPL-eu-cont-az2-Output\"\n        }\n    ],\n    \"streams\": [\n        {\n            \"streamId\": \"b7e0b573afaaf311ae6a6364be4a15bc70d8d2eb2e256ab5\",\n            \"streamName\": \"MATCH-1-BACKUP-WF-SPL-eu-cont-az2\"\n        }\n    ]\n};\n\n// Store configuration in flow context\nflow.set('edgeConfig', edgeConfig);\n\n// Pass configuration to next node\nmsg.edgeConfig = edgeConfig;\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 200, "y": 100, "wires": [["orchestrator_001"]]}, {"id": "inject_timer_001", "type": "inject", "z": "77bf3dd067e362fd", "name": "10-Second Sync Timer", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "10", "crontab": "", "once": true, "onceDelay": 0.1, "topic": "sync", "payload": "", "payloadType": "date", "x": 200, "y": 160, "wires": [["config_node_001"]]}, {"id": "orchestrator_001", "type": "function", "z": "77bf3dd067e362fd", "name": "Dynamic API Orchestrator", "func": "// Get configuration from flow context or message\nconst config = msg.edgeConfig || flow.get('edgeConfig');\nif (!config) {\n    node.error('No edge configuration found');\n    return null;\n}\n\n// Generate unique group ID for this batch\nconst groupId = msg._groupId || Date.now().toString();\n\n// Calculate total parts count\nconst totalParts = config.streams.length + config.sources.length + config.outputs.length;\n\n// Base message template\nconst baseMsg = {\n    _groupId: groupId,\n    techex: {\n        baseUrlProd: global.get('SECRETS.TECHEX_TXCORE_URL_PROD'),\n        apiKeyProd: global.get('SECRETS.TECHEX_TXCORE_API_KEY_PROD'),\n        edgeId: config.edgeId\n    }\n};\n\nconst messages = [];\nlet partIndex = 0;\n\n// Generate stream requests\nconfig.streams.forEach((stream, streamIdx) => {\n    const streamMsg = JSON.parse(JSON.stringify(baseMsg));\n    streamMsg.techex.streamId = stream.streamId;\n    streamMsg.topic = 'stream';\n    streamMsg.parts = {\n        id: groupId,\n        index: partIndex++,\n        count: totalParts\n    };\n    streamMsg._delay = 0; // No delay for streams\n    messages.push(streamMsg);\n});\n\n// Generate source requests with staggered delays\nconfig.sources.forEach((source, sourceIdx) => {\n    const sourceMsg = JSON.parse(JSON.stringify(baseMsg));\n    sourceMsg.techex.sourceId = source.sourceId;\n    sourceMsg.topic = `source${sourceIdx}`;\n    sourceMsg.parts = {\n        id: groupId,\n        index: partIndex++,\n        count: totalParts\n    };\n    sourceMsg._delay = (sourceIdx + 1) * 1000; // 1s, 2s delays\n    messages.push(sourceMsg);\n});\n\n// Generate output requests\nconfig.outputs.forEach((output, outputIdx) => {\n    const outputMsg = JSON.parse(JSON.stringify(baseMsg));\n    outputMsg.techex.outputId = output.outputId;\n    outputMsg.topic = 'output';\n    outputMsg.parts = {\n        id: groupId,\n        index: partIndex++,\n        count: totalParts\n    };\n    outputMsg._delay = (config.sources.length + 1) * 1000; // After all sources\n    messages.push(outputMsg);\n});\n\nreturn [messages];", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 450, "y": 130, "wires": [["delay_router_001"]]}, {"id": "delay_router_001", "type": "function", "z": "77bf3dd067e362fd", "name": "Delay Router", "func": "// Route messages based on delay requirements\nconst delay = msg._delay || 0;\n\nif (delay === 0) {\n    // No delay - send immediately\n    return [msg, null, null, null];\n} else if (delay === 1000) {\n    // 1 second delay\n    return [null, msg, null, null];\n} else if (delay === 2000) {\n    // 2 second delay\n    return [null, null, msg, null];\n} else {\n    // 3+ second delay\n    return [null, null, null, msg];\n}", "outputs": 4, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 700, "y": 130, "wires": [["api_processor_001"], ["delay_1s_001"], ["delay_2s_001"], ["delay_3s_001"]]}, {"id": "delay_1s_001", "type": "delay", "z": "77bf3dd067e362fd", "name": "1s Delay", "pauseType": "delay", "timeout": "1", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 900, "y": 160, "wires": [["api_processor_001"]]}, {"id": "delay_2s_001", "type": "delay", "z": "77bf3dd067e362fd", "name": "2s <PERSON><PERSON>", "pauseType": "delay", "timeout": "2", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 900, "y": 200, "wires": [["api_processor_001"]]}, {"id": "delay_3s_001", "type": "delay", "z": "77bf3dd067e362fd", "name": "3s <PERSON><PERSON>", "pauseType": "delay", "timeout": "3", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 900, "y": 240, "wires": [["api_processor_001"]]}, {"id": "api_processor_001", "type": "function", "z": "77bf3dd067e362fd", "name": "Dynamic API Processor", "func": "// Extract configuration\nconst baseUrl = msg.techex?.baseUrlProd;\nconst token = msg.techex?.apiKeyProd;\nconst edgeId = msg.techex?.edgeId;\n\n// Validate required configuration\nif (!baseUrl || !token || !edgeId) {\n    return [null, {\n        error: \"Missing configuration: baseUrlProd, apiKeyProd, or edgeId\",\n        statusCode: 400,\n        details: { hasBaseUrl: !!baseUrl, hasToken: !!token, hasEdgeId: !!edgeId }\n    }];\n}\n\n// Determine endpoint based on topic\nlet endpoint = '';\nlet resourceId = '';\n\nif (msg.topic === 'stream') {\n    resourceId = msg.techex.streamId;\n    if (!resourceId) {\n        return [null, { error: \"Missing streamId\", statusCode: 400 }];\n    }\n    endpoint = `/api/mwedge/${encodeURIComponent(edgeId)}/stream/${encodeURIComponent(resourceId)}`;\n} else if (msg.topic.startsWith('source')) {\n    resourceId = msg.techex.sourceId;\n    if (!resourceId) {\n        return [null, { error: \"Missing sourceId\", statusCode: 400 }];\n    }\n    endpoint = `/api/mwedge/${encodeURIComponent(edgeId)}/source/${encodeURIComponent(resourceId)}`;\n} else if (msg.topic === 'output') {\n    resourceId = msg.techex.outputId;\n    if (!resourceId) {\n        return [null, { error: \"Missing outputId\", statusCode: 400 }];\n    }\n    endpoint = `/api/mwedge/${encodeURIComponent(edgeId)}/output/${encodeURIComponent(resourceId)}`;\n} else {\n    return [null, { error: `Unknown topic: ${msg.topic}`, statusCode: 400 }];\n}\n\n// Rate limiting (10ms interval)\nconst operationName = `api_${msg.topic}`;\nconst rlKey = `rateLimit.${operationName}`;\nconst last = flow.get(rlKey) || 0;\nconst MIN_INTERVAL = 10; // ms\nif (Date.now() - last < MIN_INTERVAL) {\n    const retryAfter = MIN_INTERVAL - (Date.now() - last);\n    return [null, {\n        error: \"Rate limit: Too many requests\",\n        statusCode: 429,\n        details: {},\n        retryAfter\n    }];\n}\nflow.set(rlKey, Date.now());\n\n// Build request\ntry {\n    msg.url = `${baseUrl.replace(/\\/$/, '')}${endpoint}`;\n    msg.method = 'GET';\n    \n    // Set headers\n    msg.headers = {\n        Authorization: `Bearer ${token}`,\n        Accept: 'application/json',\n        'Content-Type': 'application/json'\n    };\n    \n    // Visual status in editor\n    node.status({ fill: 'green', shape: 'dot', text: `${msg.method} ${msg.topic}` });\n    \n    return [msg, null];\n} catch (err) {\n    node.error('API setup failed', err);\n    return [null, {\n        error: err.message,\n        statusCode: 500,\n        details: { stack: err.stack }\n    }];\n}", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1100, "y": 200, "wires": [["http_request_001"], ["debug_error_001"]]}, {"id": "http_request_001", "type": "http request", "z": "77bf3dd067e362fd", "name": "API Request", "method": "use", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1300, "y": 200, "wires": [["header_cleanup_001"]]}, {"id": "header_cleanup_001", "type": "change", "z": "77bf3dd067e362fd", "name": "Clean Headers", "rules": [{"t": "delete", "p": "method", "pt": "msg"}, {"t": "delete", "p": "headers", "pt": "msg"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 1500, "y": 200, "wires": [["http_wrapper_001"]]}, {"id": "http_wrapper_001", "type": "function", "z": "77bf3dd067e362fd", "name": "HTTP → <PERSON><PERSON>", "func": "// Wrap HTTP response and split OK vs Error to 2 outputs\n// Output 1: normalized wrapper { ok, statusCode, data } → Join\n// Output 2: only when HTTP error (statusCode >= 400 or msg.error)\n\nconst sc = Number(msg.statusCode) || 0;\n\n// Preserve topic/parts for Join; do not touch them\nconst wrapped = {\n    ok: sc >= 200 && sc < 300,\n    statusCode: sc,\n    data: msg.payload\n};\n\nmsg.payload = wrapped;\n\n// Decide if this is an error\nconst isHttpError = !wrapped.ok || !!msg.error || sc === 0;\n\n// Optional status indicator in editor\nnode.status({\n    fill: wrapped.ok ? \"green\" : \"red\",\n    shape: wrapped.ok ? \"dot\" : \"ring\",\n    text: `HTTP ${sc || \"?\"}`\n});\n\nif (isHttpError) {\n    // Build a lightweight error copy preserving routing fields\n    const errMsg = {\n        _isHttpError: true,\n        topic: msg.topic,\n        parts: msg.parts,\n        statusCode: sc,\n        error: msg.error || null,\n        url: msg.url,\n        method: msg.method\n    };\n    return [msg, errMsg];\n}\n\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1700, "y": 200, "wires": [["dynamic_join_001"], ["debug_http_error_001"]]}, {"id": "dynamic_join_001", "type": "join", "z": "77bf3dd067e362fd", "name": "Dynamic Join (by topic, per groupId)", "mode": "manual", "build": "object", "property": "payload", "propertyType": "msg", "key": "topic", "joiner": "\\n", "joinerType": "str", "useparts": true, "accumulate": false, "timeout": "10", "count": "", "reduceRight": false, "reduceExp": "", "reduceInit": "", "reduceInitType": "", "reduceFixup": "", "x": 1900, "y": 200, "wires": [["gate_retry_001"]]}, {"id": "gate_retry_001", "type": "function", "z": "77bf3dd067e362fd", "name": "Gate & Retry on 429", "func": "// Get expected configuration to validate completeness\nconst config = flow.get('edgeConfig');\nif (!config) {\n    node.error('No edge configuration found for validation');\n    return [null, msg];\n}\n\n// Expected topics based on configuration\nconst expectedTopics = [];\nconfig.streams.forEach(() => expectedTopics.push('stream'));\nconfig.sources.forEach((_, idx) => expectedTopics.push(`source${idx}`));\nconfig.outputs.forEach(() => expectedTopics.push('output'));\n\nconst p = msg.payload || {};\nconst receivedTopics = Object.keys(p);\n\n// Check if we have all expected topics\nconst isComplete = expectedTopics.every(topic => receivedTopics.includes(topic));\n\nif (!isComplete) {\n    node.status({fill:\"grey\", shape:\"ring\", text:\"incomplete join → retry\"});\n    return [null, msg];\n}\n\n// Check for rate limiting (429 errors)\nconst values = Object.values(p);\nconst any429 = values.some(v => v && v.statusCode === 429);\nconst anyHardErr = values.some(v => v && v.ok === false && v.statusCode >= 400);\n\nif (any429) {\n    node.status({fill:\"yellow\", shape:\"ring\", text:\"rate limited (429) – retry 10s\"});\n    msg.topic = \"warning\";\n    msg._reason = \"429\";\n    return [null, msg];\n}\n\nif (anyHardErr) {\n    node.status({fill:\"red\", shape:\"dot\", text:\"one or more API errors\"});\n    // Still pass through so UI can show last known state\n    return [msg, null];\n}\n\nnode.status({fill:\"green\", shape:\"dot\", text:\"OK\"});\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 2100, "y": 200, "wires": [["state_mapper_001"], ["retry_handler_001"]]}, {"id": "state_mapper_001", "type": "function", "z": "77bf3dd067e362fd", "name": "Map → Dynamic State Model", "func": "// Get configuration for mapping\nconst config = flow.get('edgeConfig');\nif (!config) {\n    node.error('No edge configuration found for mapping');\n    return null;\n}\n\nconst j = msg.payload || {};\n\n// Safely unwrap the envelope we created after each HTTP request\nfunction unwrap(x) {\n    if (x && typeof x === 'object' && 'data' in x) return x.data || {};\n    return x || {};\n}\n\nfunction nameOf(o, fallback) {\n    if (!o || typeof o !== 'object') return fallback;\n    return o.name || o.displayName || o.label || fallback;\n}\n\nfunction isActive(o) {\n    if (!o || typeof o !== 'object') return false;\n    if (typeof o.active === 'boolean') return o.active;\n    if (typeof o.isActive === 'boolean') return o.isActive;\n    if (typeof o.online === 'boolean') return o.online;\n    if (typeof o.status === 'string') return /up|on|online|active/i.test(o.status);\n    return false;\n}\n\n// Unwrap all responses\nconst stream = unwrap(j.stream);\nconst sources = [];\nconst outputs = [];\n\n// Process sources dynamically\nconfig.sources.forEach((sourceConfig, idx) => {\n    const sourceData = unwrap(j[`source${idx}`]);\n    sources.push({\n        name: nameOf(sourceData, sourceConfig.sourceName || `Source ${idx + 1}`),\n        active: isActive(sourceData),\n        type: sourceConfig.type,\n        sourceId: sourceConfig.sourceId\n    });\n});\n\n// Process outputs\nconfig.outputs.forEach((outputConfig, idx) => {\n    const outputData = unwrap(j.output);\n    outputs.push({\n        name: nameOf(outputData, outputConfig.outputName || `Output ${idx + 1}`)\n    });\n});\n\n// Build dynamic model\nconst model = {\n    configuredStreams: [{ name: nameOf(stream, config.streams[0]?.streamName || \"--\") }],\n    configuredSources: sources,\n    configuredOutputs: outputs,\n    online: [stream, ...sources.map(s => ({ active: s.active }))].some(x => isActive(x) || x.active),\n    edgeConfig: config\n};\n\nmsg.payload = model;\n\n// Determine current active source for UI\nconst activeSource = sources.find(s => s.active);\nif (activeSource) {\n    if (activeSource.type === 'Main') {\n        msg.currentSource = 'main';\n    } else if (activeSource.type === 'Backup') {\n        msg.currentSource = 'backup';\n    } else {\n        msg.currentSource = activeSource.type.toLowerCase();\n    }\n} else {\n    msg.currentSource = null;\n}\n\nnode.status({ fill: \"green\", shape: \"dot\", text: \"mapped → dynamic ui model\" });\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 2300, "y": 160, "wires": [["change_detector_001"]]}, {"id": "change_detector_001", "type": "function", "z": "77bf3dd067e362fd", "name": "Detect Source Change", "func": "// Get the current source from the incoming message\nconst newSource = msg.currentSource;\n\n// Get the previous source state from flow context\nlet oldSource = flow.get('current_source_state');\nif (oldSource === undefined) {\n    oldSource = null;\n}\n\n// Compare the current state with the previous state\nif (newSource !== oldSource) {\n    // A change has been detected\n    flow.set('current_source_state', newSource);\n    \n    // Create a new message with the change details\n    msg.payload = newSource;\n    msg.change = `Source changed from ${oldSource || 'initial'} to ${newSource}`;\n    \n    // Return the message to trigger switch logic\n    return [msg, msg]; // Send to both UI and switch handler\n}\n\n// If there's no change, only send to UI\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 2500, "y": 160, "wires": [["ui_template_001"], ["switch_handler_001"]]}, {"id": "retry_handler_001", "type": "change", "z": "77bf3dd067e362fd", "name": "Reset Group & Clean for Retry", "rules": [{"t": "delete", "p": "parts", "pt": "msg"}, {"t": "delete", "p": "topic", "pt": "msg"}, {"t": "delete", "p": "payload", "pt": "msg"}, {"t": "set", "p": "_retry", "pt": "msg", "to": "429", "tot": "str"}], "x": 2100, "y": 260, "wires": [["retry_delay_001"]]}, {"id": "retry_delay_001", "type": "delay", "z": "77bf3dd067e362fd", "name": "Retry after 10s", "pauseType": "delay", "timeout": "10", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 2300, "y": 260, "wires": [["retry_cleanup_001"]]}, {"id": "retry_cleanup_001", "type": "change", "z": "77bf3dd067e362fd", "name": "Clean All for Retry", "rules": [{"t": "delete", "p": "_msgid", "pt": "msg"}, {"t": "delete", "p": "_groupId", "pt": "msg"}, {"t": "delete", "p": "techex", "pt": "msg"}, {"t": "delete", "p": "_retry", "pt": "msg"}], "x": 2500, "y": 260, "wires": [["config_node_001"]]}, {"id": "switch_handler_001", "type": "function", "z": "77bf3dd067e362fd", "name": "Format Switch API Request", "func": "// Get the new source from the previous node's output\nconst newSource = msg.payload;\nconst config = flow.get('edgeConfig');\n\nif (!config || !newSource) {\n    return null;\n}\n\n// Log the request intent\nnode.log(`[REQ] API call to switch source to: ${newSource}`);\n\n// Find the source configuration\nlet targetSource = null;\nif (newSource === 'main') {\n    targetSource = config.sources.find(s => s.type === 'Main');\n} else if (newSource === 'backup') {\n    targetSource = config.sources.find(s => s.type === 'Backup');\n} else {\n    targetSource = config.sources.find(s => s.type.toLowerCase() === newSource.toLowerCase());\n}\n\nif (!targetSource) {\n    node.error(`No source found for type: ${newSource}`);\n    return null;\n}\n\nconst baseUrl = global.get('SECRETS.TECHEX_TXCORE_URL_PROD');\nconst apiKey = global.get('SECRETS.TECHEX_TXCORE_API_KEY_PROD');\n\n// Create the message for the API call to activate the selected source\nmsg.url = `${baseUrl}/api/mwedge/${config.edgeId}/source/${targetSource.sourceId}`;\nmsg.method = 'PUT';\nmsg.headers = {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n    'Authorization': `Bearer ${apiKey}`\n};\nmsg.payload = { active: true };\nmsg.currentSource = newSource;\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 2700, "y": 200, "wires": [["switch_http_001"]]}, {"id": "switch_http_001", "type": "http request", "z": "77bf3dd067e362fd", "name": "Switch API Call", "method": "use", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 2900, "y": 200, "wires": [["switch_response_001"]]}, {"id": "switch_response_001", "type": "function", "z": "77bf3dd067e362fd", "name": "Handle Switch Response", "func": "const statusCode = msg.statusCode || 0;\nconst time = new Date().toISOString();\n\n// Error path: If the API call fails\nif (statusCode !== 200) {\n    const errorPayload = {\n        success: false,\n        action: `Switch to ${msg.currentSource}`,\n        error: `API call failed with status ${statusCode}`,\n        details: `Response: ${JSON.stringify(msg.payload)}`,\n        timestamp: time\n    };\n    \n    msg.payload = errorPayload;\n    msg.topic = \"error\";\n    \n    node.error(`[ERROR] Switch API returned ${statusCode}`, msg);\n    return [null, msg];\n}\n\n// Success path: If the API call is successful\nconst newSource = msg.currentSource;\nconst successPayload = {\n    success: true,\n    action: `Switched to ${newSource}`,\n    message: `Source successfully switched to '${newSource}'`,\n    timestamp: time\n};\n\nmsg.payload = successPayload;\nmsg.topic = \"success\";\n\nnode.log(`[SUCCESS] Source switched to '${newSource}'`);\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 3100, "y": 200, "wires": [["debug_switch_success_001"], ["debug_switch_error_001"]]}, {"id": "debug_error_001", "type": "debug", "z": "77bf3dd067e362fd", "name": "Debug: API Setup Error", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1100, "y": 260, "wires": []}, {"id": "debug_http_error_001", "type": "debug", "z": "77bf3dd067e362fd", "name": "Debug: HTT<PERSON> Error", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1700, "y": 260, "wires": []}, {"id": "debug_switch_success_001", "type": "debug", "z": "77bf3dd067e362fd", "name": "Debug: Switch Success", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 3300, "y": 160, "wires": []}, {"id": "debug_switch_error_001", "type": "debug", "z": "77bf3dd067e362fd", "name": "Debug: <PERSON><PERSON> E<PERSON>r", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 3300, "y": 240, "wires": []}, {"id": "ui_template_001", "type": "ui_template", "z": "77bf3dd067e362fd", "group": "ui_group_001", "name": "Dynamic Source Control UI", "order": 1, "width": "8", "height": "6", "format": "<style>\n    body {\n        background-color: #1e1e1e;\n        font-family: 'Fira Code', monospace;\n        color: #ccc;\n        margin: 0;\n        padding: 10px;\n    }\n\n    .card {\n        background-color: #252526;\n        border-radius: 10px;\n        padding: 15px;\n        width: 100%;\n        box-shadow: 0 0 10px rgba(0,0,0,0.3);\n        box-sizing: border-box;\n    }\n\n    .card-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        border-bottom: 1px solid #333;\n        padding-bottom: 5px;\n        margin-bottom: 15px;\n    }\n\n    .title {\n        display: flex;\n        align-items: center;\n        gap: 6px;\n        font-weight: bold;\n        color: #ccc;\n    }\n\n    .title svg {\n        cursor: pointer;\n        width: 24px;\n        height: 24px;\n        fill: silver;\n        transition: fill 0.2s ease-in-out;\n    }\n\n    .status-time {\n        font-size: 0.85rem;\n        color: #66c2a5;\n    }\n\n    .label {\n        color: #888;\n        margin-bottom: 2px;\n        font-size: 0.9rem;\n    }\n\n    .value {\n        color: #66c2a5;\n        font-size: 0.95rem;\n        margin-bottom: 10px;\n    }\n\n    .value-main {\n        color: #ccc;\n        font-size: 1rem;\n        margin-bottom: 10px;\n    }\n\n    .source-buttons {\n        display: flex;\n        gap: 5px;\n        margin-bottom: 10px;\n        flex-wrap: wrap;\n    }\n\n    .source-btn {\n        background-color: #3a8dde;\n        color: white;\n        padding: 6px 14px;\n        border: none;\n        border-radius: 5px;\n        cursor: pointer;\n        font-family: 'Fira Code', monospace;\n        transition: background-color 0.2s;\n        flex: 1;\n        min-width: 80px;\n    }\n\n    .source-btn:hover:not(:disabled) {\n        background-color: #2d77c7;\n    }\n\n    .source-btn:disabled {\n        background-color: #2a2a2a;\n        color: #666;\n        cursor: not-allowed;\n    }\n\n    .source-btn.active {\n        background-color: #66c2a5;\n        color: #1e1e1e;\n    }\n\n    select {\n        width: 100%;\n        padding: 8px;\n        background-color: #1e1e1e;\n        color: #ccc;\n        border: 1px solid #444;\n        border-radius: 5px;\n        font-family: 'Fira Code', monospace;\n    }\n\n    select:disabled {\n        background-color: #2a2a2a;\n        color: #666;\n        border-color: #333;\n        cursor: not-allowed;\n    }\n\n    .flex-row {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 10px;\n    }\n\n    .online-indicator {\n        display: inline-block;\n        width: 8px;\n        height: 8px;\n        border-radius: 50%;\n        margin-right: 5px;\n    }\n\n    .online-indicator.online {\n        background-color: #66c2a5;\n    }\n\n    .online-indicator.offline {\n        background-color: #ef4444;\n    }\n</style>\n\n<div class=\"card\">\n    <div class=\"card-header\">\n        <div class=\"title\">\n            <svg id=\"lockIcon\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 17a2 2 0 100-4 2 2 0 000 4zm6-7h-1V7a5 5 0 00-10 0v3H6a2 2 0 00-2 2v8a2 2 0 002 \n                2h12a2 2 0 002-2v-8a2 2 0 00-2-2zm-8-3a3 3 0 016 0v3H10V7z\"/>\n            </svg>\n            <span id=\"edgeName\">Dynamic Source Control</span>\n        </div>\n        <div class=\"status-time\">\n            <span class=\"online-indicator\" id=\"onlineIndicator\"></span>\n            <span id=\"time\">--:--:--</span>\n        </div>\n    </div>\n\n    <div>\n        <div class=\"label\">Active Stream:</div>\n        <div class=\"value\" id=\"activeStream\">--</div>\n\n        <div class=\"label\">Current Active Source:</div>\n        <div class=\"flex-row\">\n            <div class=\"value-main\" id=\"currentSource\">--</div>\n        </div>\n\n        <div class=\"label\">Source Selection:</div>\n        <div class=\"source-buttons\" id=\"sourceButtons\">\n            <!-- Dynamic buttons will be generated here -->\n        </div>\n\n        <div class=\"label\">Source Dropdown:</div>\n        <select id=\"sourceSelect\" disabled>\n            <option>Loading...</option>\n        </select>\n\n        <div class=\"label\" style=\"margin-top: 10px;\">Output Stream:</div>\n        <div class=\"value\" id=\"outputStream\">--</div>\n    </div>\n</div>\n\n<script>\n(function(scope) {\n    const lockIcon = document.getElementById('lockIcon');\n    const sourceButtons = document.getElementById('sourceButtons');\n    const sourceSelect = document.getElementById('sourceSelect');\n    const edgeName = document.getElementById('edgeName');\n    const activeStream = document.getElementById('activeStream');\n    const currentSource = document.getElementById('currentSource');\n    const outputStream = document.getElementById('outputStream');\n    const onlineIndicator = document.getElementById('onlineIndicator');\n    const timeElement = document.getElementById('time');\n\n    let locked = true;\n    let currentConfig = null;\n    let currentState = null;\n\n    const lockSVG = `<path d=\"M12 17a2 2 0 100-4 2 2 0 000 4zm6-7h-1V7a5 5 0 00-10 0v3H6a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2v-8a2 2 0 00-2-2zm-8-3a3 3 0 016 0v3H10V7z\"/>`;\n    const unlockSVG = `<path d=\"M17 8V7a5 5 0 00-9.9-1h2.1a3 3 0 015.8 1v1H8a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-8a2 2 0 00-2-2h-1z\"/>`;\n\n    function toggleLock() {\n        locked = !locked;\n        lockIcon.innerHTML = locked ? lockSVG : unlockSVG;\n        updateButtonStates();\n    }\n\n    function updateButtonStates() {\n        const buttons = sourceButtons.querySelectorAll('.source-btn');\n        buttons.forEach(btn => {\n            btn.disabled = locked;\n        });\n        sourceSelect.disabled = locked;\n    }\n\n    function createSourceButtons(sources) {\n        sourceButtons.innerHTML = '';\n        sourceSelect.innerHTML = '';\n        \n        sources.forEach((source, index) => {\n            // Create button\n            const button = document.createElement('button');\n            button.className = 'source-btn';\n            button.textContent = source.type || `Source ${index + 1}`;\n            button.disabled = locked;\n            button.addEventListener('click', () => {\n                if (!locked) {\n                    switchToSource(source.type.toLowerCase());\n                }\n            });\n            sourceButtons.appendChild(button);\n            \n            // Create option\n            const option = document.createElement('option');\n            option.value = source.type.toLowerCase();\n            option.textContent = source.type;\n            sourceSelect.appendChild(option);\n        });\n    }\n\n    function updateActiveSource(activeSourceType) {\n        const buttons = sourceButtons.querySelectorAll('.source-btn');\n        buttons.forEach(btn => {\n            btn.classList.remove('active');\n            if (btn.textContent.toLowerCase() === activeSourceType) {\n                btn.classList.add('active');\n            }\n        });\n        \n        sourceSelect.value = activeSourceType || '';\n        currentSource.textContent = activeSourceType ? activeSourceType.charAt(0).toUpperCase() + activeSourceType.slice(1) : '--';\n    }\n\n    function switchToSource(sourceType) {\n        if (locked) return;\n        \n        // Send message back to Node-RED\n        scope.send({\n            currentSource: sourceType,\n            action: 'switch',\n            timestamp: new Date().toISOString()\n        });\n    }\n\n    function updateTime() {\n        const now = new Date();\n        timeElement.textContent = now.toLocaleTimeString();\n    }\n\n    // Initialize\n    lockIcon.addEventListener('click', toggleLock);\n    lockIcon.innerHTML = lockSVG;\n    setInterval(updateTime, 1000);\n    updateTime();\n\n    // Handle dropdown changes\n    sourceSelect.addEventListener('change', (e) => {\n        if (!locked && e.target.value) {\n            switchToSource(e.target.value);\n        }\n    });\n\n    // Watch for incoming messages from Node-RED\n    scope.$watch('msg', function(msg) {\n        if (!msg || !msg.payload) return;\n        \n        const payload = msg.payload;\n        \n        // Update configuration if available\n        if (payload.edgeConfig) {\n            currentConfig = payload.edgeConfig;\n            edgeName.textContent = currentConfig.edgeName || 'Dynamic Source Control';\n        }\n        \n        // Update UI elements\n        if (payload.configuredStreams && payload.configuredStreams.length > 0) {\n            activeStream.textContent = payload.configuredStreams[0].name;\n        }\n        \n        if (payload.configuredOutputs && payload.configuredOutputs.length > 0) {\n            outputStream.textContent = payload.configuredOutputs[0].name;\n        }\n        \n        if (payload.configuredSources) {\n            createSourceButtons(payload.configuredSources);\n        }\n        \n        // Update online status\n        if (typeof payload.online === 'boolean') {\n            onlineIndicator.className = `online-indicator ${payload.online ? 'online' : 'offline'}`;\n        }\n        \n        // Update active source\n        if (msg.currentSource) {\n            updateActiveSource(msg.currentSource);\n        }\n        \n        currentState = payload;\n    });\n\n})(scope);\n</script>", "storeOutMessages": true, "fwdInMessages": true, "resendOnRefresh": false, "templateScope": "local", "className": "", "x": 2700, "y": 120, "wires": [["user_input_handler_001"]]}, {"id": "user_input_handler_001", "type": "function", "z": "77bf3dd067e362fd", "name": "Handle UI Input", "func": "// Handle user input from the UI\nif (msg.action === 'switch' && msg.currentSource) {\n    // User clicked a source switch button\n    const config = flow.get('edgeConfig');\n    if (!config) {\n        node.error('No edge configuration found');\n        return null;\n    }\n    \n    // Find the target source\n    let targetSource = null;\n    if (msg.currentSource === 'main') {\n        targetSource = config.sources.find(s => s.type === 'Main');\n    } else if (msg.currentSource === 'backup') {\n        targetSource = config.sources.find(s => s.type === 'Backup');\n    } else {\n        targetSource = config.sources.find(s => s.type.toLowerCase() === msg.currentSource.toLowerCase());\n    }\n    \n    if (!targetSource) {\n        node.error(`No source found for type: ${msg.currentSource}`);\n        return null;\n    }\n    \n    // Prepare message for switch handler\n    msg.payload = msg.currentSource;\n    msg.change = `User requested switch to ${msg.currentSource}`;\n    \n    // Add configuration context\n    msg.techex = {\n        baseUrlProd: global.get('SECRETS.TECHEX_TXCORE_URL_PROD'),\n        apiKeyProd: global.get('SECRETS.TECHEX_TXCORE_API_KEY_PROD'),\n        edgeId: config.edgeId\n    };\n    \n    return msg;\n}\n\n// For other message types, just pass through\nreturn null;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 2900, "y": 120, "wires": [["switch_handler_001"]]}, {"id": "ui_group_001", "type": "ui_group", "name": "Dynamic Source Control", "tab": "ui_tab_001", "order": 1, "disp": true, "width": "8", "collapse": false, "className": ""}, {"id": "ui_tab_001", "type": "ui_tab", "name": "Source Management", "icon": "dashboard", "disabled": false, "hidden": false}]