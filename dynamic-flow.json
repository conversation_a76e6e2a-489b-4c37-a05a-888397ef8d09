[{"id": "efa2913198fc597d", "type": "tab", "label": "Dynamic Source Flow", "disabled": false, "info": "", "env": []}, {"id": "56cf3043103b36d9", "type": "group", "z": "efa2913198fc597d", "name": "[001] - Configuration & Timer", "style": {"label": true, "fill-opacity": "0.5"}, "nodes": ["2aaae9a238ab92e2", "cfdb8081198784f5", "cb935c4f4645cafc"], "x": 74, "y": 139, "w": 332, "h": 142}, {"id": "d482ee32f88e203f", "type": "group", "z": "efa2913198fc597d", "name": "[002] - Dynamic API Orchestration", "style": {"label": true, "fill-opacity": "0.5"}, "nodes": ["e45a5f218793767c", "26a38539dac83a01", "0e2a60cedc6caca9", "050c6f1ac2449157", "aee7005a697bbbba"], "x": 464, "y": 84, "w": 462, "h": 257}, {"id": "4062682232f24383", "type": "group", "z": "efa2913198fc597d", "name": "[003] - API Processing & HTTP Requests", "style": {"label": true, "fill-opacity": "0.5"}, "nodes": ["90dd4a946ff3728d", "add26f541a1661a1", "f25f53ea6e7ffd57", "0169f606174471f3"], "x": 974, "y": 139, "w": 392, "h": 142}, {"id": "6dbb5a3eab58b9eb", "type": "group", "z": "efa2913198fc597d", "name": "[004] - Response Handling & Join", "style": {"label": true, "fill-opacity": "0.5"}, "nodes": ["ed313b4819c8d975", "936139c8564fbcbe", "7cce419572aadea8", "cf9c56c919f4c065", "b5cf980d8832817e", "7c1b58c197f2abfe", "b9faf2e66f22414d", "a17b5e23725a4c05"], "x": 974, "y": 339, "w": 532, "h": 202}, {"id": "93faefc9363a0048", "type": "group", "z": "efa2913198fc597d", "name": "[005] - State Management & Mapping", "style": {"label": true, "fill-opacity": "0.5"}, "nodes": ["b9a825fd337aa41c", "0c6dbe0d6f0feca5"], "x": 1514, "y": 159, "w": 236, "h": 82}, {"id": "5f7e701153e62997", "type": "group", "z": "efa2913198fc597d", "name": "[006] - User Interface", "style": {"label": true, "fill-opacity": "0.5"}, "nodes": ["e2da5f28611343bc", "4289f57ee7310752"], "x": 1814, "y": 159, "w": 152, "h": 82}, {"id": "6e7e7976e8b075e2", "type": "group", "z": "efa2913198fc597d", "name": "[007] - Source Switching Logic", "style": {"label": true, "fill-opacity": "0.5"}, "nodes": ["6d74e20e74638bec", "9df2cbb09867eb84", "3e07026a673c0e56", "b19e6a0abdb5090c", "6b1683c75dede34a"], "x": 1614, "y": 339, "w": 252, "h": 202}, {"id": "2aaae9a238ab92e2", "type": "function", "z": "efa2913198fc597d", "g": "56cf3043103b36d9", "name": "Configuration Store", "func": "// Dynamic Edge Configuration\nconst edgeConfig = {\n    \"edgeId\": \"6885dadf539d77930a6e3f8a\",\n    \"edgeName\": \"MATCH-1-BACKUP-WF-eu-cont-az2\",\n    \"sources\": [\n        {\n            \"sourceId\": \"2fb6f4fca6f9af1b48b35d8adc835002bcec7679cfbe6b7f\",\n            \"sourceName\": \"MATCH-1-BACKUP-WF-SPL-eu-cont-az2-Input A\",\n            \"type\": \"Main\"\n        },\n        {\n            \"sourceId\": \"93d4edffa63c83a188312c1e267286b0078e9ad1fa35f6ac\",\n            \"sourceName\": \"MATCH-1-BACKUP-WF-eu-cont-az2-Input Caller\",\n            \"type\": \"Backup\"\n        }\n    ],\n    \"outputs\": [\n        {\n            \"outputId\": \"d045eb67e21e173a49622e96b8af30deac7cfc147d11a9c3\",\n            \"outputName\": \"MATCH-1-BACKUP-WF-SPL-eu-cont-az2-Output\"\n        }\n    ],\n    \"streams\": [\n        {\n            \"streamId\": \"b7e0b573afaaf311ae6a6364be4a15bc70d8d2eb2e256ab5\",\n            \"streamName\": \"MATCH-1-BACKUP-WF-SPL-eu-cont-az2\"\n        }\n    ]\n};\n\n// Store configuration in flow context\nflow.set('edgeConfig', edgeConfig);\n\n// Pass configuration to next node\nmsg.edgeConfig = edgeConfig;\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 290, "y": 180, "wires": [["e45a5f218793767c"]]}, {"id": "cfdb8081198784f5", "type": "inject", "z": "efa2913198fc597d", "g": "56cf3043103b36d9", "name": "10-Second Sync Timer", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "10", "crontab": "", "once": true, "onceDelay": 0.1, "topic": "sync", "payload": "", "payloadType": "date", "x": 135, "y": 180, "wires": [["2aaae9a238ab92e2"]], "icon": "node-red-contrib-loop-processing/loop.png", "l": false}, {"id": "e45a5f218793767c", "type": "function", "z": "efa2913198fc597d", "g": "d482ee32f88e203f", "name": "Dynamic API Orchestrator", "func": "// Get configuration from flow context or message\nconst config = msg.edgeConfig || flow.get('edgeConfig');\nif (!config) {\n    node.error('No edge configuration found');\n    return null;\n}\n\n// Generate unique group ID for this batch\nconst groupId = msg._groupId || Date.now().toString();\n\n// Calculate total parts count\nconst totalParts = config.streams.length + config.sources.length + config.outputs.length;\n\n// Base message template\nconst baseMsg = {\n    _groupId: groupId,\n    techex: {\n        baseUrlProd: global.get('SECRETS.TECHEX_TXCORE_URL_PROD'),\n        apiKeyProd: global.get('SECRETS.TECHEX_TXCORE_API_KEY_PROD'),\n        edgeId: config.edgeId\n    }\n};\n\nconst messages = [];\nlet partIndex = 0;\n\n// Generate stream requests\nconfig.streams.forEach((stream, streamIdx) => {\n    const streamMsg = JSON.parse(JSON.stringify(baseMsg));\n    streamMsg.techex.streamId = stream.streamId;\n    streamMsg.topic = 'stream';\n    streamMsg.parts = {\n        id: groupId,\n        index: partIndex++,\n        count: totalParts\n    };\n    streamMsg._delay = 0; // No delay for streams\n    messages.push(streamMsg);\n});\n\n// Generate source requests with staggered delays\nconfig.sources.forEach((source, sourceIdx) => {\n    const sourceMsg = JSON.parse(JSON.stringify(baseMsg));\n    sourceMsg.techex.sourceId = source.sourceId;\n    sourceMsg.topic = `source${sourceIdx}`;\n    sourceMsg.parts = {\n        id: groupId,\n        index: partIndex++,\n        count: totalParts\n    };\n    sourceMsg._delay = (sourceIdx + 1) * 1000; // 1s, 2s delays\n    messages.push(sourceMsg);\n});\n\n// Generate output requests\nconfig.outputs.forEach((output, outputIdx) => {\n    const outputMsg = JSON.parse(JSON.stringify(baseMsg));\n    outputMsg.techex.outputId = output.outputId;\n    outputMsg.topic = 'output';\n    outputMsg.parts = {\n        id: groupId,\n        index: partIndex++,\n        count: totalParts\n    };\n    outputMsg._delay = (config.sources.length + 1) * 1000; // After all sources\n    messages.push(outputMsg);\n});\n\nreturn [messages];", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 505, "y": 140, "wires": [["26a38539dac83a01"]], "l": false}, {"id": "26a38539dac83a01", "type": "function", "z": "efa2913198fc597d", "g": "d482ee32f88e203f", "name": "Delay Router", "func": "// Route messages based on delay requirements\nconst delay = msg._delay || 0;\n\nif (delay === 0) {\n    // No delay - send immediately\n    return [msg, null, null, null];\n} else if (delay === 1000) {\n    // 1 second delay\n    return [null, msg, null, null];\n} else if (delay === 2000) {\n    // 2 second delay\n    return [null, null, msg, null];\n} else {\n    // 3+ second delay\n    return [null, null, null, msg];\n}", "outputs": 4, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 630, "y": 140, "wires": [["90dd4a946ff3728d"], ["0e2a60cedc6caca9"], ["050c6f1ac2449157"], ["aee7005a697bbbba"]]}, {"id": "0e2a60cedc6caca9", "type": "delay", "z": "efa2913198fc597d", "g": "d482ee32f88e203f", "name": "1s Delay", "pauseType": "delay", "timeout": "1", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 840, "y": 180, "wires": [["90dd4a946ff3728d"]]}, {"id": "050c6f1ac2449157", "type": "delay", "z": "efa2913198fc597d", "g": "d482ee32f88e203f", "name": "2s <PERSON><PERSON>", "pauseType": "delay", "timeout": "2", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 840, "y": 240, "wires": [["90dd4a946ff3728d"]]}, {"id": "90dd4a946ff3728d", "type": "function", "z": "efa2913198fc597d", "g": "4062682232f24383", "name": "Dynamic API Processor", "func": "// Extract configuration\nconst baseUrl = msg.techex?.baseUrlProd;\nconst token = msg.techex?.apiKeyProd;\nconst edgeId = msg.techex?.edgeId;\n\n// Validate required configuration\nif (!baseUrl || !token || !edgeId) {\n    return [null, {\n        error: \"Missing configuration: baseUrlProd, apiKeyProd, or edgeId\",\n        statusCode: 400,\n        details: { hasBaseUrl: !!baseUrl, hasToken: !!token, hasEdgeId: !!edgeId }\n    }];\n}\n\n// Determine endpoint based on topic\nlet endpoint = '';\nlet resourceId = '';\n\nif (msg.topic === 'stream') {\n    resourceId = msg.techex.streamId;\n    if (!resourceId) {\n        return [null, { error: \"Missing streamId\", statusCode: 400 }];\n    }\n    endpoint = `/api/mwedge/${encodeURIComponent(edgeId)}/stream/${encodeURIComponent(resourceId)}`;\n} else if (msg.topic.startsWith('source')) {\n    resourceId = msg.techex.sourceId;\n    if (!resourceId) {\n        return [null, { error: \"Missing sourceId\", statusCode: 400 }];\n    }\n    endpoint = `/api/mwedge/${encodeURIComponent(edgeId)}/source/${encodeURIComponent(resourceId)}`;\n} else if (msg.topic === 'output') {\n    resourceId = msg.techex.outputId;\n    if (!resourceId) {\n        return [null, { error: \"Missing outputId\", statusCode: 400 }];\n    }\n    endpoint = `/api/mwedge/${encodeURIComponent(edgeId)}/output/${encodeURIComponent(resourceId)}`;\n} else {\n    return [null, { error: `Unknown topic: ${msg.topic}`, statusCode: 400 }];\n}\n\n// Rate limiting (10ms interval)\nconst operationName = `api_${msg.topic}`;\nconst rlKey = `rateLimit.${operationName}`;\nconst last = flow.get(rlKey) || 0;\nconst MIN_INTERVAL = 10; // ms\nif (Date.now() - last < MIN_INTERVAL) {\n    const retryAfter = MIN_INTERVAL - (Date.now() - last);\n    return [null, {\n        error: \"Rate limit: Too many requests\",\n        statusCode: 429,\n        details: {},\n        retryAfter\n    }];\n}\nflow.set(rlKey, Date.now());\n\n// Build request\ntry {\n    msg.url = `${baseUrl.replace(/\\/$/, '')}${endpoint}`;\n    msg.method = 'GET';\n    \n    // Set headers\n    msg.headers = {\n        Authorization: `Bearer ${token}`,\n        Accept: 'application/json',\n        'Content-Type': 'application/json'\n    };\n    \n    // Visual status in editor\n    node.status({ fill: 'green', shape: 'dot', text: `${msg.method} ${msg.topic}` });\n    \n    return [msg, null];\n} catch (err) {\n    node.error('API setup failed', err);\n    return [null, {\n        error: err.message,\n        statusCode: 500,\n        details: { stack: err.stack }\n    }];\n}", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1100, "y": 180, "wires": [["add26f541a1661a1"], ["0169f606174471f3"]]}, {"id": "add26f541a1661a1", "type": "http request", "z": "efa2913198fc597d", "g": "4062682232f24383", "name": "API Request", "method": "use", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1245, "y": 180, "wires": [["f25f53ea6e7ffd57"]], "l": false}, {"id": "f25f53ea6e7ffd57", "type": "change", "z": "efa2913198fc597d", "g": "4062682232f24383", "name": "Clean Headers", "rules": [{"t": "delete", "p": "method", "pt": "msg"}, {"t": "delete", "p": "headers", "pt": "msg"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 1325, "y": 180, "wires": [["ed313b4819c8d975"]], "l": false}, {"id": "ed313b4819c8d975", "type": "function", "z": "efa2913198fc597d", "g": "6dbb5a3eab58b9eb", "name": "HTTP → <PERSON><PERSON>", "func": "// Wrap HTTP response and split OK vs Error to 2 outputs\n// Output 1: normalized wrapper { ok, statusCode, data } → Join\n// Output 2: only when HTTP error (statusCode >= 400 or msg.error)\n\nconst sc = Number(msg.statusCode) || 0;\n\n// Preserve topic/parts for Join; do not touch them\nconst wrapped = {\n    ok: sc >= 200 && sc < 300,\n    statusCode: sc,\n    data: msg.payload\n};\n\nmsg.payload = wrapped;\n\n// Decide if this is an error\nconst isHttpError = !wrapped.ok || !!msg.error || sc === 0;\n\n// Optional status indicator in editor\nnode.status({\n    fill: wrapped.ok ? \"green\" : \"red\",\n    shape: wrapped.ok ? \"dot\" : \"ring\",\n    text: `HTTP ${sc || \"?\"}`\n});\n\nif (isHttpError) {\n    // Build a lightweight error copy preserving routing fields\n    const errMsg = {\n        _isHttpError: true,\n        topic: msg.topic,\n        parts: msg.parts,\n        statusCode: sc,\n        error: msg.error || null,\n        url: msg.url,\n        method: msg.method\n    };\n    return [msg, errMsg];\n}\n\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1015, "y": 380, "wires": [["936139c8564fbcbe"], ["b9faf2e66f22414d"]], "l": false}, {"id": "936139c8564fbcbe", "type": "join", "z": "efa2913198fc597d", "g": "6dbb5a3eab58b9eb", "name": "Dynamic Join (by topic, per groupId)", "mode": "manual", "build": "object", "property": "payload", "propertyType": "msg", "key": "topic", "joiner": "\\n", "joinerType": "str", "useparts": true, "accumulate": false, "timeout": "10", "count": "", "reduceRight": false, "reduceExp": "", "reduceInit": "", "reduceInitType": "", "reduceFixup": "", "x": 1105, "y": 380, "wires": [["7cce419572aadea8"]], "l": false}, {"id": "7cce419572aadea8", "type": "function", "z": "efa2913198fc597d", "g": "6dbb5a3eab58b9eb", "name": "Gate & Retry on 429", "func": "// Get expected configuration to validate completeness\nconst config = flow.get('edgeConfig');\nif (!config) {\n    node.error('No edge configuration found for validation');\n    return [null, msg];\n}\n\n// Expected topics based on configuration\nconst expectedTopics = [];\nconfig.streams.forEach(() => expectedTopics.push('stream'));\nconfig.sources.forEach((_, idx) => expectedTopics.push(`source${idx}`));\nconfig.outputs.forEach(() => expectedTopics.push('output'));\n\nconst p = msg.payload || {};\nconst receivedTopics = Object.keys(p);\n\n// Check if we have all expected topics\nconst isComplete = expectedTopics.every(topic => receivedTopics.includes(topic));\n\nif (!isComplete) {\n    node.status({fill:\"grey\", shape:\"ring\", text:\"incomplete join → retry\"});\n    return [null, msg];\n}\n\n// Check for rate limiting (429 errors)\nconst values = Object.values(p);\nconst any429 = values.some(v => v && v.statusCode === 429);\nconst anyHardErr = values.some(v => v && v.ok === false && v.statusCode >= 400);\n\nif (any429) {\n    node.status({fill:\"yellow\", shape:\"ring\", text:\"rate limited (429) – retry 10s\"});\n    msg.topic = \"warning\";\n    msg._reason = \"429\";\n    return [null, msg];\n}\n\nif (anyHardErr) {\n    node.status({fill:\"red\", shape:\"dot\", text:\"one or more API errors\"});\n    // Still pass through so UI can show last known state\n    return [msg, null];\n}\n\nnode.status({fill:\"green\", shape:\"dot\", text:\"OK\"});\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1175, "y": 380, "wires": [["b9a825fd337aa41c"], ["cf9c56c919f4c065"]], "l": false}, {"id": "b9a825fd337aa41c", "type": "function", "z": "efa2913198fc597d", "g": "93faefc9363a0048", "name": "Map → Dynamic State Model", "func": "// Get configuration for mapping\nconst config = flow.get('edgeConfig');\nif (!config) {\n    node.error('No edge configuration found for mapping');\n    return null;\n}\n\nconst j = msg.payload || {};\n\n// Safely unwrap the envelope we created after each HTTP request\nfunction unwrap(x) {\n    if (x && typeof x === 'object' && 'data' in x) return x.data || {};\n    return x || {};\n}\n\nfunction nameOf(o, fallback) {\n    if (!o || typeof o !== 'object') return fallback;\n    return o.name || o.displayName || o.label || fallback;\n}\n\nfunction isActive(o) {\n    if (!o || typeof o !== 'object') return false;\n    if (typeof o.active === 'boolean') return o.active;\n    if (typeof o.isActive === 'boolean') return o.isActive;\n    if (typeof o.online === 'boolean') return o.online;\n    if (typeof o.status === 'string') return /up|on|online|active/i.test(o.status);\n    return false;\n}\n\n// Unwrap all responses\nconst stream = unwrap(j.stream);\nconst sources = [];\nconst outputs = [];\n\n// Process sources dynamically\nconfig.sources.forEach((sourceConfig, idx) => {\n    const sourceData = unwrap(j[`source${idx}`]);\n    sources.push({\n        name: nameOf(sourceData, sourceConfig.sourceName || `Source ${idx + 1}`),\n        active: isActive(sourceData),\n        type: sourceConfig.type,\n        sourceId: sourceConfig.sourceId\n    });\n});\n\n// Process outputs\nconfig.outputs.forEach((outputConfig, idx) => {\n    const outputData = unwrap(j.output);\n    outputs.push({\n        name: nameOf(outputData, outputConfig.outputName || `Output ${idx + 1}`)\n    });\n});\n\n// Build dynamic model\nconst model = {\n    configuredStreams: [{ name: nameOf(stream, config.streams[0]?.streamName || \"--\") }],\n    configuredSources: sources,\n    configuredOutputs: outputs,\n    online: [stream, ...sources.map(s => ({ active: s.active }))].some(x => isActive(x) || x.active),\n    edgeConfig: config\n};\n\nmsg.payload = model;\n\n// Determine current active source for UI\nconst activeSource = sources.find(s => s.active);\nif (activeSource) {\n    if (activeSource.type === 'Main') {\n        msg.currentSource = 'main';\n    } else if (activeSource.type === 'Backup') {\n        msg.currentSource = 'backup';\n    } else {\n        msg.currentSource = activeSource.type.toLowerCase();\n    }\n} else {\n    msg.currentSource = null;\n}\n\nnode.status({ fill: \"green\", shape: \"dot\", text: \"mapped → dynamic ui model\" });\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1555, "y": 200, "wires": [["0c6dbe0d6f0feca5"]], "l": false}, {"id": "0c6dbe0d6f0feca5", "type": "function", "z": "efa2913198fc597d", "g": "93faefc9363a0048", "name": "Detect Source Change", "func": "// Get the current source from the incoming message\nconst newSource = msg.currentSource;\n\n// Get the previous source state from flow context\nlet oldSource = flow.get('current_source_state');\nif (oldSource === undefined) {\n    oldSource = null;\n}\n\n// Compare the current state with the previous state\nif (newSource !== oldSource) {\n    // A change has been detected\n    flow.set('current_source_state', newSource);\n    \n    // Create a new message with the change details\n    msg.payload = newSource;\n    msg.change = `Source changed from ${oldSource || 'initial'} to ${newSource}`;\n    \n    // Return the message to trigger switch logic\n    return [msg, msg]; // Send to both UI and switch handler\n}\n\n// If there's no change, only send to UI\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1635, "y": 200, "wires": [["e2da5f28611343bc"], ["6d74e20e74638bec"]], "l": false}, {"id": "cf9c56c919f4c065", "type": "change", "z": "efa2913198fc597d", "g": "6dbb5a3eab58b9eb", "name": "Reset Group & Clean for Retry", "rules": [{"t": "delete", "p": "parts", "pt": "msg"}, {"t": "delete", "p": "topic", "pt": "msg"}, {"t": "delete", "p": "payload", "pt": "msg"}, {"t": "set", "p": "_retry", "pt": "msg", "to": "429", "tot": "str"}], "x": 1175, "y": 440, "wires": [["b5cf980d8832817e"]], "l": false}, {"id": "b5cf980d8832817e", "type": "delay", "z": "efa2913198fc597d", "g": "6dbb5a3eab58b9eb", "name": "Retry after 10s", "pauseType": "delay", "timeout": "10", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 1320, "y": 440, "wires": [["7c1b58c197f2abfe"]]}, {"id": "7c1b58c197f2abfe", "type": "change", "z": "efa2913198fc597d", "g": "6dbb5a3eab58b9eb", "name": "Clean All for Retry", "rules": [{"t": "delete", "p": "_msgid", "pt": "msg"}, {"t": "delete", "p": "_groupId", "pt": "msg"}, {"t": "delete", "p": "techex", "pt": "msg"}, {"t": "delete", "p": "_retry", "pt": "msg"}], "x": 1455, "y": 440, "wires": [["a17b5e23725a4c05"]], "l": false}, {"id": "6d74e20e74638bec", "type": "function", "z": "efa2913198fc597d", "g": "6e7e7976e8b075e2", "name": "Format Switch API Request", "func": "// Get the new source from the previous node's output\nconst newSource = msg.payload;\nconst config = flow.get('edgeConfig');\n\nif (!config || !newSource) {\n    return null;\n}\n\n// Log the request intent\nnode.log(`[REQ] API call to switch source to: ${newSource}`);\n\n// Find the source configuration\nlet targetSource = null;\nif (newSource === 'main') {\n    targetSource = config.sources.find(s => s.type === 'Main');\n} else if (newSource === 'backup') {\n    targetSource = config.sources.find(s => s.type === 'Backup');\n} else {\n    targetSource = config.sources.find(s => s.type.toLowerCase() === newSource.toLowerCase());\n}\n\nif (!targetSource) {\n    node.error(`No source found for type: ${newSource}`);\n    return null;\n}\n\nconst baseUrl = global.get('SECRETS.TECHEX_TXCORE_URL_PROD');\nconst apiKey = global.get('SECRETS.TECHEX_TXCORE_API_KEY_PROD');\n\n// Create the message for the API call to activate the selected source\nmsg.url = `${baseUrl}/api/mwedge/${config.edgeId}/source/${targetSource.sourceId}`;\nmsg.method = 'PUT';\nmsg.headers = {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n    'Authorization': `Bearer ${apiKey}`\n};\nmsg.payload = { active: true };\nmsg.currentSource = newSource;\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1655, "y": 440, "wires": [["9df2cbb09867eb84"]], "l": false}, {"id": "9df2cbb09867eb84", "type": "http request", "z": "efa2913198fc597d", "g": "6e7e7976e8b075e2", "name": "Switch API Call", "method": "use", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1725, "y": 440, "wires": [["3e07026a673c0e56"]], "l": false}, {"id": "3e07026a673c0e56", "type": "function", "z": "efa2913198fc597d", "g": "6e7e7976e8b075e2", "name": "Handle Switch Response", "func": "const statusCode = msg.statusCode || 0;\nconst time = new Date().toISOString();\n\n// Error path: If the API call fails\nif (statusCode !== 200) {\n    const errorPayload = {\n        success: false,\n        action: `Switch to ${msg.currentSource}`,\n        error: `API call failed with status ${statusCode}`,\n        details: `Response: ${JSON.stringify(msg.payload)}`,\n        timestamp: time\n    };\n    \n    msg.payload = errorPayload;\n    msg.topic = \"error\";\n    \n    node.error(`[ERROR] Switch API returned ${statusCode}`, msg);\n    return [null, msg, msg]; // Send to debug (null), error debug, and UI\n}\n\n// Success path: If the API call is successful\nconst newSource = msg.currentSource;\nconst successPayload = {\n    success: true,\n    action: `Switched to ${newSource}`,\n    message: `Source successfully switched to '${newSource}'`,\n    timestamp: time\n};\n\nmsg.payload = successPayload;\nmsg.topic = \"success\";\n\nnode.log(`[SUCCESS] Source switched to '${newSource}'`);\nreturn [msg, null, msg]; // Send success to debug, error (null), and UI", "outputs": 3, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1805, "y": 440, "wires": [["6b1683c75dede34a"], ["b19e6a0abdb5090c"], ["e2da5f28611343bc"]], "l": false}, {"id": "0169f606174471f3", "type": "debug", "z": "efa2913198fc597d", "g": "4062682232f24383", "name": "Debug: API Setup Error", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1175, "y": 240, "wires": [], "icon": "node-red/alert.svg", "l": false}, {"id": "b9faf2e66f22414d", "type": "debug", "z": "efa2913198fc597d", "g": "6dbb5a3eab58b9eb", "name": "Debug: HTT<PERSON> Error", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1015, "y": 440, "wires": [], "icon": "node-red/alert.svg", "l": false}, {"id": "6b1683c75dede34a", "type": "debug", "z": "efa2913198fc597d", "g": "6e7e7976e8b075e2", "name": "Debug: Switch Success", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "payload", "statusType": "auto", "x": 1805, "y": 380, "wires": [], "icon": "node-red/alert.svg", "l": false}, {"id": "b19e6a0abdb5090c", "type": "debug", "z": "efa2913198fc597d", "g": "6e7e7976e8b075e2", "name": "Debug: <PERSON><PERSON> E<PERSON>r", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "payload", "statusType": "auto", "x": 1805, "y": 500, "wires": [], "icon": "node-red/alert.svg", "l": false}, {"id": "e2da5f28611343bc", "type": "ui_template", "z": "efa2913198fc597d", "g": "5f7e701153e62997", "group": "eec528bd137eed65", "name": "Dynamic Source Control UI", "order": 1, "width": "0", "height": "0", "format": "<style>\n    body {\n        background-color: #1e1e1e;\n        font-family: 'Fira Code', monospace;\n        color: #ccc;\n        margin: 0;\n        padding: 10px;\n    }\n\n    .card {\n        background-color: #252526;\n        border-radius: 10px;\n        padding: 15px;\n        width: 100%;\n        box-shadow: 0 0 10px rgba(0,0,0,0.3);\n        box-sizing: border-box;\n    }\n\n    .card-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        border-bottom: 1px solid #333;\n        padding-bottom: 5px;\n        margin-bottom: 15px;\n    }\n\n    .title {\n        display: flex;\n        align-items: center;\n        gap: 6px;\n        font-weight: bold;\n        color: #ccc;\n    }\n\n    .title svg {\n        cursor: pointer;\n        width: 24px;\n        height: 24px;\n        fill: silver;\n        transition: fill 0.2s ease-in-out;\n    }\n\n    .status-time {\n        font-size: 0.85rem;\n        color: #66c2a5;\n    }\n\n    .label {\n        color: #888;\n        margin-bottom: 2px;\n        font-size: 0.9rem;\n    }\n\n    .value {\n        color: #66c2a5;\n        font-size: 0.95rem;\n        margin-bottom: 10px;\n    }\n\n    .value-main {\n        color: #ccc;\n        font-size: 1rem;\n        margin-bottom: 5px;\n        word-wrap: break-word;\n        line-height: 1.2;\n    }\n\n    .source-name {\n        color: #66c2a5;\n        font-size: 0.85rem;\n        margin-bottom: 10px;\n        word-wrap: break-word;\n        line-height: 1.2;\n    }\n\n    .switch-btn {\n        background-color: #3a8dde;\n        color: white;\n        padding: 6px 14px;\n        border: none;\n        border-radius: 5px;\n        cursor: pointer;\n        font-family: 'Fira Code', monospace;\n        transition: background-color 0.2s;\n        min-width: 100px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        gap: 5px;\n    }\n\n    .switch-btn:hover:not(:disabled) {\n        background-color: #2d77c7;\n    }\n\n    .switch-btn:disabled {\n        background-color: #2a2a2a;\n        color: #666;\n        cursor: not-allowed;\n    }\n\n    .switch-btn.loading {\n        background-color: #f59e0b;\n        cursor: not-allowed;\n    }\n\n    .flex-row {\n        display: flex;\n        justify-content: space-between;\n        align-items: flex-start;\n        margin-bottom: 10px;\n        gap: 10px;\n    }\n\n    .source-info {\n        flex: 1;\n        min-width: 0;\n    }\n\n    .online-indicator {\n        display: inline-block;\n        width: 8px;\n        height: 8px;\n        border-radius: 50%;\n        margin-right: 5px;\n    }\n\n    .online-indicator.online {\n        background-color: #66c2a5;\n    }\n\n    .online-indicator.offline {\n        background-color: #ef4444;\n    }\n\n    .spinner {\n        width: 12px;\n        height: 12px;\n        border: 2px solid #ffffff33;\n        border-top: 2px solid #ffffff;\n        border-radius: 50%;\n        animation: spin 1s linear infinite;\n    }\n\n    @keyframes spin {\n        0% { transform: rotate(0deg); }\n        100% { transform: rotate(360deg); }\n    }\n\n    .last-updated {\n        font-size: 0.75rem;\n        color: #888;\n        margin-left: 5px;\n    }\n</style>\n\n<div class=\"card\">\n    <div class=\"card-header\">\n        <div class=\"title\">\n            <svg id=\"lockIcon\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 17a2 2 0 100-4 2 2 0 000 4zm6-7h-1V7a5 5 0 00-10 0v3H6a2 2 0 00-2 2v8a2 2 0 002 \n                2h12a2 2 0 002-2v-8a2 2 0 00-2-2zm-8-3a3 3 0 016 0v3H10V7z\"/>\n            </svg>\n            <span id=\"edgeName\">Dynamic Source Control</span>\n        </div>\n        <div class=\"status-time\">\n            <span class=\"online-indicator\" id=\"onlineIndicator\"></span>\n            <span id=\"lastUpdated\">Last Updated: --</span>\n        </div>\n    </div>\n\n    <div>\n        <div class=\"label\">Active Stream:</div>\n        <div class=\"value\" id=\"activeStream\">--</div>\n\n        <div class=\"label\">Current Active Source:</div>\n        <div class=\"flex-row\">\n            <div class=\"source-info\">\n                <div class=\"value-main\" id=\"currentSource\">--</div>\n                <div class=\"source-name\" id=\"sourceName\">--</div>\n            </div>\n            <button class=\"switch-btn\" id=\"switchBtn\" disabled>\n                <span id=\"switchBtnText\">Switch Now</span>\n            </button>\n        </div>\n\n        <div class=\"label\" style=\"margin-top: 10px;\">Output Stream:</div>\n        <div class=\"value\" id=\"outputStream\">--</div>\n    </div>\n</div>\n\n<script>\n(function(scope) {\n    const lockIcon = document.getElementById('lockIcon');\n    const edgeName = document.getElementById('edgeName');\n    const activeStream = document.getElementById('activeStream');\n    const currentSource = document.getElementById('currentSource');\n    const sourceName = document.getElementById('sourceName');\n    const outputStream = document.getElementById('outputStream');\n    const onlineIndicator = document.getElementById('onlineIndicator');\n    const lastUpdated = document.getElementById('lastUpdated');\n    const switchBtn = document.getElementById('switchBtn');\n    const switchBtnText = document.getElementById('switchBtnText');\n\n    let locked = true;\n    let currentConfig = null;\n    let currentState = null;\n    let availableSources = [];\n    let currentActiveIndex = 0;\n    let isLoading = false;\n\n    const lockSVG = `<path d=\"M12 17a2 2 0 100-4 2 2 0 000 4zm6-7h-1V7a5 5 0 00-10 0v3H6a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2v-8a2 2 0 00-2-2zm-8-3a3 3 0 016 0v3H10V7z\"/>`;\n    const unlockSVG = `<path d=\"M17 8V7a5 5 0 00-9.9-1h2.1a3 3 0 015.8 1v1H8a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-8a2 2 0 00-2-2h-1z\"/>`;\n\n    function toggleLock() {\n        locked = !locked;\n        lockIcon.innerHTML = locked ? lockSVG : unlockSVG;\n        updateButtonStates();\n    }\n\n    function updateButtonStates() {\n        switchBtn.disabled = locked || isLoading;\n    }\n\n    function setLoadingState(loading) {\n        isLoading = loading;\n        if (loading) {\n            switchBtn.classList.add('loading');\n            switchBtn.disabled = true;\n            switchBtnText.innerHTML = '<div class=\"spinner\"></div> Switching...';\n        } else {\n            switchBtn.classList.remove('loading');\n            switchBtnText.textContent = 'Switch Now';\n            updateButtonStates();\n        }\n    }\n\n    function getNextSource() {\n        if (availableSources.length <= 1) return null;\n        \n        const nextIndex = (currentActiveIndex + 1) % availableSources.length;\n        return availableSources[nextIndex];\n    }\n\n    function updateActiveSource(activeSourceType, sources) {\n        if (!sources) sources = availableSources;\n        \n        // Find current active source index\n        currentActiveIndex = sources.findIndex(s => s.type.toLowerCase() === activeSourceType);\n        if (currentActiveIndex === -1) currentActiveIndex = 0;\n        \n        const activeSource = sources[currentActiveIndex];\n        if (activeSource) {\n            currentSource.textContent = activeSource.type || '--';\n            sourceName.textContent = activeSource.name || activeSource.sourceName || '--';\n        } else {\n            currentSource.textContent = '--';\n            sourceName.textContent = '--';\n        }\n    }\n\n    function switchToNextSource() {\n        if (locked || isLoading) return;\n        \n        const nextSource = getNextSource();\n        if (!nextSource) return;\n        \n        setLoadingState(true);\n        \n        // Send message back to Node-RED\n        scope.send({\n            currentSource: nextSource.type.toLowerCase(),\n            action: 'switch',\n            timestamp: new Date().toISOString()\n        });\n    }\n\n    function updateLastUpdated() {\n        const now = new Date();\n        lastUpdated.textContent = `Last Updated: ${now.toLocaleTimeString()}`;\n    }\n\n    function triggerRefresh() {\n        // Send refresh trigger to Node-RED\n        scope.send({\n            action: 'refresh',\n            timestamp: new Date().toISOString()\n        });\n    }\n\n    // Initialize\n    lockIcon.addEventListener('click', toggleLock);\n    lockIcon.innerHTML = lockSVG;\n    switchBtn.addEventListener('click', switchToNextSource);\n    \n    // Watch for incoming messages from Node-RED\n    scope.$watch('msg', function(msg) {\n        if (!msg || !msg.payload) return;\n        \n        const payload = msg.payload;\n        \n        // Handle switch response messages\n        if (msg.topic === 'success' && payload.success) {\n            setLoadingState(false);\n            updateLastUpdated();\n            // Trigger immediate refresh after successful switch\n            setTimeout(triggerRefresh, 500);\n            return;\n        }\n        \n        if (msg.topic === 'error' && payload.success === false) {\n            setLoadingState(false);\n            // Could add error notification here\n            return;\n        }\n        \n        // Update configuration if available\n        if (payload.edgeConfig) {\n            currentConfig = payload.edgeConfig;\n            edgeName.textContent = currentConfig.edgeName || 'Dynamic Source Control';\n        }\n        \n        // Update UI elements\n        if (payload.configuredStreams && payload.configuredStreams.length > 0) {\n            activeStream.textContent = payload.configuredStreams[0].name;\n        }\n        \n        if (payload.configuredOutputs && payload.configuredOutputs.length > 0) {\n            outputStream.textContent = payload.configuredOutputs[0].name;\n        }\n        \n        if (payload.configuredSources) {\n            availableSources = payload.configuredSources;\n        }\n        \n        // Update online status\n        if (typeof payload.online === 'boolean') {\n            onlineIndicator.className = `online-indicator ${payload.online ? 'online' : 'offline'}`;\n        }\n        \n        // Update active source\n        if (msg.currentSource) {\n            updateActiveSource(msg.currentSource, availableSources);\n        }\n        \n        // Update last updated timestamp\n        updateLastUpdated();\n        \n        currentState = payload;\n    });\n\n})(scope);\n</script>", "storeOutMessages": true, "fwdInMessages": true, "resendOnRefresh": false, "templateScope": "local", "className": "", "x": 1855, "y": 200, "wires": [["4289f57ee7310752"]], "l": false}, {"id": "4289f57ee7310752", "type": "function", "z": "efa2913198fc597d", "g": "5f7e701153e62997", "name": "Handle UI Input", "func": "// Handle user input from the UI\nif (msg.action === 'switch' && msg.currentSource) {\n    // User clicked the switch button\n    const config = flow.get('edgeConfig');\n    if (!config) {\n        node.error('No edge configuration found');\n        return null;\n    }\n    \n    // Find the target source\n    let targetSource = null;\n    if (msg.currentSource === 'main') {\n        targetSource = config.sources.find(s => s.type === 'Main');\n    } else if (msg.currentSource === 'backup') {\n        targetSource = config.sources.find(s => s.type === 'Backup');\n    } else {\n        targetSource = config.sources.find(s => s.type.toLowerCase() === msg.currentSource.toLowerCase());\n    }\n    \n    if (!targetSource) {\n        node.error(`No source found for type: ${msg.currentSource}`);\n        return null;\n    }\n    \n    // Prepare message for switch handler\n    msg.payload = msg.currentSource;\n    msg.change = `User requested switch to ${msg.currentSource}`;\n    \n    // Add configuration context\n    msg.techex = {\n        baseUrlProd: global.get('SECRETS.TECHEX_TXCORE_URL_PROD'),\n        apiKeyProd: global.get('SECRETS.TECHEX_TXCORE_API_KEY_PROD'),\n        edgeId: config.edgeId\n    };\n    \n    return [msg, null]; // Send to switch handler\n}\n\nif (msg.action === 'refresh') {\n    // User triggered a refresh - send to configuration node\n    return [null, msg]; // Send to config refresh\n}\n\n// For other message types, just pass through\nreturn [null, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1925, "y": 200, "wires": [["6d74e20e74638bec"], ["2aaae9a238ab92e2"]], "l": false}, {"id": "aee7005a697bbbba", "type": "delay", "z": "efa2913198fc597d", "g": "d482ee32f88e203f", "name": "3s <PERSON><PERSON>", "pauseType": "delay", "timeout": "3", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 840, "y": 300, "wires": [["90dd4a946ff3728d"]]}, {"id": "cb935c4f4645cafc", "type": "link in", "z": "efa2913198fc597d", "g": "56cf3043103b36d9", "name": "link in 5", "links": ["a17b5e23725a4c05"], "x": 165, "y": 240, "wires": [["2aaae9a238ab92e2"]]}, {"id": "a17b5e23725a4c05", "type": "link out", "z": "efa2913198fc597d", "g": "6dbb5a3eab58b9eb", "name": "link out 1", "mode": "link", "links": ["cb935c4f4645cafc"], "x": 1465, "y": 500, "wires": []}, {"id": "eec528bd137eed65", "type": "ui_group", "name": "Dynamic Source Control", "tab": "7377faa6ecaf78ab", "order": 1, "disp": false, "width": "22", "collapse": false, "className": ""}, {"id": "7377faa6ecaf78ab", "type": "ui_tab", "name": "Source Management", "icon": "dashboard", "disabled": false, "hidden": false}]