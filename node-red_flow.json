[{"id": "f2fddda16bbb2e61", "type": "group", "z": "77bf3dd067e362fd", "name": "[003] Get a source Main", "style": {"label": true}, "nodes": ["45173c1a6926cd2b", "5c43b317c74b01ec", "1640eb66df995d5f", "90c04a7db49af46c", "b5b1693ac00c1b31", "131cf446b5fe643b", "07303f6914377e96", "fdd40ecf07b1cd48", "e890197fa6b9df27", "7e50193de8946657"], "x": 1434, "y": 999, "w": 402, "h": 202}, {"id": "45173c1a6926cd2b", "type": "http request", "z": "77bf3dd067e362fd", "g": "f2fddda16bbb2e61", "name": "", "method": "use", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1645, "y": 1100, "wires": [["5c43b317c74b01ec", "e890197fa6b9df27"]], "l": false}, {"id": "5c43b317c74b01ec", "type": "debug", "z": "77bf3dd067e362fd", "g": "f2fddda16bbb2e61", "name": "Debug OK: TEXCORE Get TXEdge Source", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1645, "y": 1040, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "1640eb66df995d5f", "type": "debug", "z": "77bf3dd067e362fd", "g": "f2fddda16bbb2e61", "name": "Debug Error: TEXCORE Get TXEdge Source", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1575, "y": 1160, "wires": [], "icon": "node-red/alert.svg", "l": false, "info": "# Flow Debug Error Get Secret\n\n## Description\nThis `Debug` node is used to log and inspect errors that occur during the secret retrieval process. It captures the full message object (`msg`) and displays the `msg.statusCode` in the node's status field for quick monitoring.\n\n## Input\n- **Trigger:**\n  - The node is triggered by the output of a preceding node in the flow, typically when an error occurs during secret retrieval.\n- **Context / Global Vars:** None required.\n- **Expected Secrets:** None required.\n\n## Output\n- **Success Path:**\n  - Logs the full incoming message (`msg`) to the debug sidebar for inspection.\n  - Displays the `msg.statusCode` in the node's status field for quick reference.\n  - Outputs the message for further processing if connected downstream.\n- **Error Path:**\n  - This node does not handle errors but is used to inspect and debug issues in the incoming message.\n\n## Notes\n> - The debug node is enabled by default to assist in troubleshooting.\n> - Use this node during development or troubleshooting to identify and resolve issues in the secret retrieval process.\n> - The `msg.statusCode` is displayed in the node's status field for quick monitoring of the HTTP response status.\n\n## Author\n[<NAME_EMAIL>](mailto:<EMAIL>)\n\n## qibb Docs\n[📚 qibb Platform Documentation](https://docs.qibb.com/platform/latest/)\n"}, {"id": "90c04a7db49af46c", "type": "function", "z": "77bf3dd067e362fd", "g": "f2fddda16bbb2e61", "name": "Get TXEdge Source", "func": "// 1. Extract configuration\nconst baseUrl = msg.techex?.baseUrlProd;\nconst token   = msg.techex?.apiKeyProd;\n\n// 2. Validate required configuration\nif (!baseUrl || !token) {\n    return [null, {\n        error: \"Missing configuration: baseUrlProd or apiKeyProd\",\n        statusCode: 400,\n        details: { hasBaseUrl: !!baseUrl, hasToken: !!token }\n    }];\n}\n\n// 3. Validate path parameters\nconst mwedgeId = msg.techex.edgeId      // preferred location\n\nconst sourceIdMain = msg.techex?.sourceIdMain || msg.sourceIdMain;\n\nif (!mwedgeId || !sourceIdMain) {\n    return [null, {\n        error: \"Missing path parameter: _id or sourceId\",\n        statusCode: 400,\n        details: { hasId: !!mwedgeId, hasSourceId: !!sourceIdMain }\n    }];\n}\n\n// 4. Simple rate limiting (1 request / second)\nconst operationName = \"GetMWEdgeSource\";\nconst rlKey = `rateLimit.${operationName}`;\nconst last  = flow.get(rlKey) || 0;\nconst MIN_INTERVAL = 10; // ms\nif (Date.now() - last < MIN_INTERVAL) {\n    const retryAfter = MIN_INTERVAL - (Date.now() - last);\n    return [null, {\n        error: \"Rate limit: Too many requests\",\n        statusCode: 429,\n        details: {},\n        retryAfter\n    }];\n}\nflow.set(rlKey, Date.now());\n\n// 5. Build request\ntry {\n    // Construct URL with both path parameters\n    msg.url = `${baseUrl.replace(/\\/$/, '')}/api/mwedge/${encodeURIComponent(mwedgeId)}/source/${encodeURIComponent(sourceIdMain)}`;\n\n    msg.method = msg.method || 'GET';\n\n    // Append query parameters if provided\n    if (msg.params && typeof msg.params === 'object') {\n        const query = Object.entries(msg.params)\n            .map(([k, v]) => `${encodeURIComponent(k)}=${encodeURIComponent(v)}`)\n            .join('&');\n        if (query) msg.url += `?${query}`;\n    }\n\n    // Set headers\n    msg.headers = {\n        ...(msg.headers || {}),\n        Authorization: `Bearer ${token}`,\n        Accept: 'application/json',\n        'Content-Type': 'application/json'\n    };\n\n    // Visual status in editor\n    node.status({ fill: 'green', shape: 'dot', text: `${msg.method}` });\n\n    // msg.statusCode = 200;\n    return [msg, null];\n} catch (err) {\n    node.error('API setup failed', err);\n    return [null, {\n        error: err.message,\n        statusCode: 500,\n        details: { stack: err.stack }\n    }];\n}", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1575, "y": 1100, "wires": [["45173c1a6926cd2b"], ["1640eb66df995d5f"]], "l": false, "info": "# Get MWEdge Source\n\n## Purpose\nRetrieve details for **a specific source on a specific MWEdge**.\n\n## API Details\n- **Endpoint**: `GET /api/mwedge/:_id/source/:sourceId`\n- **Authentication**: Bearer token\n- **Content-Type**: `application/json`\n\n## Required Inputs\n| Field | Location | Description |\n|-------|----------|-------------|\n| `msg.techex.baseUrlProd` | msg.techex | Base URL of the TechEx Core (e.g. `https://txcore`) |\n| `msg.techex.apiKeyProd`  | msg.techex | API/JWT token |\n| `_id` | (`msg.techex._id`, `msg._id`, etc.) | **MWEdge unique ID** |\n| `sourceId` | (`msg.techex.sourceId`, `msg.sourceId`) | **Unique source ID** |\n\n### Optional\n- `msg.method`  – Override HTTP method (default **GET**)\n- `msg.params`  – Object of query parameters to append\n\n## Outputs\n- **Output 1** – Success: full `msg` prepared for the HTTP Request node (URL, headers, etc.)\n- **Output 2** – Error object `{ error, statusCode, details }`\n\n## Error Scenarios\n- Missing configuration (**400**)\n- Missing `_id` or `sourceId` (**400**)\n- Rate-limit triggered (**429**)\n- Internal setup failure (**500**)\n\n---\n*Author: <EMAIL>*"}, {"id": "b5b1693ac00c1b31", "type": "change", "z": "77bf3dd067e362fd", "g": "f2fddda16bbb2e61", "name": "Set topic: source<PERSON>ain", "rules": [{"t": "set", "p": "topic", "pt": "msg", "to": "sourceMain", "tot": "str"}, {"t": "set", "p": "parts", "pt": "msg", "to": "{ \"id\": _groupId, \"index\": 1, \"count\": 4 }", "tot": "jsonata"}], "x": 1475, "y": 1100, "wires": [["7e50193de8946657"]], "l": false}, {"id": "131cf446b5fe643b", "type": "function", "z": "77bf3dd067e362fd", "g": "f2fddda16bbb2e61", "name": "HTTP → Join wrapper (with error side-output)", "func": "// Wrap HTTP response and split OK vs Error to 2 outputs\n// Output 1: normalized wrapper { ok, statusCode, data } → Join\n// Output 2: only when HTTP error (statusCode >= 400 or msg.error)\n\nconst sc = Number(msg.statusCode) || 0;\n\n// Preserve topic/parts for Join; do not touch them\nconst wrapped = {\n  ok: sc >= 200 && sc < 300,\n  statusCode: sc,\n  data: msg.payload\n};\n\nmsg.payload = wrapped;\n\n// Decide if this is an error\nconst isHttpError = !wrapped.ok || !!msg.error || sc === 0;\n\n// Optional status indicator in editor\nnode.status({\n  fill: wrapped.ok ? \"green\" : \"red\",\n  shape: wrapped.ok ? \"dot\" : \"ring\",\n  text: `HTTP ${sc || \"?\"}`\n});\n\nif (isHttpError) {\n  // Build a lightweight error copy preserving routing fields\n  const errMsg = {\n    _isHttpError: true,\n    topic: msg.topic,\n    parts: msg.parts,\n    statusCode: sc,\n    error: msg.error || null,\n    url: msg.url,\n    method: msg.method\n  };\n  return [msg, errMsg];\n}\n\nreturn [msg, null];\n", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1775, "y": 1100, "wires": [["fdd40ecf07b1cd48", "8cee4a563b63aca9"], ["07303f6914377e96"]], "l": false}, {"id": "07303f6914377e96", "type": "debug", "z": "77bf3dd067e362fd", "g": "f2fddda16bbb2e61", "name": "Debug Error: TechEx Get a Stream", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1775, "y": 1160, "wires": [], "icon": "node-red/alert.svg", "l": false}, {"id": "fdd40ecf07b1cd48", "type": "debug", "z": "77bf3dd067e362fd", "g": "f2fddda16bbb2e61", "name": "Debug OK: TechEx TxCore Get a Stream", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1775, "y": 1040, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "e890197fa6b9df27", "type": "change", "z": "77bf3dd067e362fd", "g": "f2fddda16bbb2e61", "name": "Clean header", "rules": [{"t": "delete", "p": "method", "pt": "msg"}, {"t": "delete", "p": "headers", "pt": "msg"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 1695, "y": 1100, "wires": [["131cf446b5fe643b"]], "l": false}, {"id": "7e50193de8946657", "type": "delay", "z": "77bf3dd067e362fd", "g": "f2fddda16bbb2e61", "name": "", "pauseType": "delay", "timeout": "1", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 1525, "y": 1100, "wires": [["90c04a7db49af46c"]], "l": false}, {"id": "925aa305e0b01b49", "type": "group", "z": "77bf3dd067e362fd", "name": "[004] Get a source Backup", "style": {"label": true}, "nodes": ["9075e24d082cc7fd", "dad9a3baed0d5f88", "e365680d914291ba", "690acfc7ed67ff4e", "698cb44a475df783", "7f2f8c1c3982f4b3", "138bea7c591acd0c", "fe0573331a6b98c2", "1e93e3484f813192", "03639636331b1f35"], "x": 1434, "y": 1279, "w": 372, "h": 202}, {"id": "9075e24d082cc7fd", "type": "http request", "z": "77bf3dd067e362fd", "g": "925aa305e0b01b49", "name": "", "method": "use", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1655, "y": 1380, "wires": [["dad9a3baed0d5f88", "1e93e3484f813192"]], "l": false}, {"id": "dad9a3baed0d5f88", "type": "debug", "z": "77bf3dd067e362fd", "g": "925aa305e0b01b49", "name": "Debug OK: TEXCORE Get TXEdge Source", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1655, "y": 1320, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "e365680d914291ba", "type": "debug", "z": "77bf3dd067e362fd", "g": "925aa305e0b01b49", "name": "Debug Error: TEXCORE Get TXEdge Source", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1595, "y": 1440, "wires": [], "icon": "node-red/alert.svg", "l": false, "info": "# Flow Debug Error Get Secret\n\n## Description\nThis `Debug` node is used to log and inspect errors that occur during the secret retrieval process. It captures the full message object (`msg`) and displays the `msg.statusCode` in the node's status field for quick monitoring.\n\n## Input\n- **Trigger:**\n  - The node is triggered by the output of a preceding node in the flow, typically when an error occurs during secret retrieval.\n- **Context / Global Vars:** None required.\n- **Expected Secrets:** None required.\n\n## Output\n- **Success Path:**\n  - Logs the full incoming message (`msg`) to the debug sidebar for inspection.\n  - Displays the `msg.statusCode` in the node's status field for quick reference.\n  - Outputs the message for further processing if connected downstream.\n- **Error Path:**\n  - This node does not handle errors but is used to inspect and debug issues in the incoming message.\n\n## Notes\n> - The debug node is enabled by default to assist in troubleshooting.\n> - Use this node during development or troubleshooting to identify and resolve issues in the secret retrieval process.\n> - The `msg.statusCode` is displayed in the node's status field for quick monitoring of the HTTP response status.\n\n## Author\n[<NAME_EMAIL>](mailto:<EMAIL>)\n\n## qibb Docs\n[📚 qibb Platform Documentation](https://docs.qibb.com/platform/latest/)\n"}, {"id": "690acfc7ed67ff4e", "type": "function", "z": "77bf3dd067e362fd", "g": "925aa305e0b01b49", "name": "Get TXEdge Source", "func": "// 1. Extract configuration\nconst baseUrl = msg.techex?.baseUrlProd;\nconst token   = msg.techex?.apiKeyProd;\n\n// 2. Validate required configuration\nif (!baseUrl || !token) {\n    return [null, {\n        error: \"Missing configuration: baseUrlProd or apiKeyProd\",\n        statusCode: 400,\n        details: { hasBaseUrl: !!baseUrl, hasToken: !!token }\n    }];\n}\n\n// 3. Validate path parameters\nconst mwedgeId = msg.techex.edgeId      // preferred location\n\nconst sourceIdBackup = msg.techex?.sourceIdBackup || msg.sourceIdBackup;\n\nif (!mwedgeId || !sourceIdBackup) {\n    return [null, {\n        error: \"Missing path parameter: _id or sourceIdBackup\",\n        statusCode: 400,\n        details: { hasId: !!mwedgeId, hasSourceId: !!sourceIdBackup }\n    }];\n}\n\n// 4. Simple rate limiting (1 request / second)\nconst operationName = \"GetMWEdgeSource\";\nconst rlKey = `rateLimit.${operationName}`;\nconst last  = flow.get(rlKey) || 0;\nconst MIN_INTERVAL = 10; // ms\nif (Date.now() - last < MIN_INTERVAL) {\n    const retryAfter = MIN_INTERVAL - (Date.now() - last);\n    return [null, {\n        error: \"Rate limit: Too many requests\",\n        statusCode: 429,\n        details: {},\n        retryAfter\n    }];\n}\nflow.set(rlKey, Date.now());\n\n// 5. Build request\ntry {\n    // Construct URL with both path parameters\n    msg.url = `${baseUrl.replace(/\\/$/, '')}/api/mwedge/${encodeURIComponent(mwedgeId)}/source/${encodeURIComponent(sourceIdBackup)}`;\n\n    msg.method = msg.method || 'GET';\n\n    // Append query parameters if provided\n    if (msg.params && typeof msg.params === 'object') {\n        const query = Object.entries(msg.params)\n            .map(([k, v]) => `${encodeURIComponent(k)}=${encodeURIComponent(v)}`)\n            .join('&');\n        if (query) msg.url += `?${query}`;\n    }\n\n    // Set headers\n    msg.headers = {\n        ...(msg.headers || {}),\n        Authorization: `Bearer ${token}`,\n        Accept: 'application/json',\n        'Content-Type': 'application/json'\n    };\n\n    // Visual status in editor\n    node.status({ fill: 'green', shape: 'dot', text: `${msg.method}` });\n\n    // msg.statusCode = 200;\n    return [msg, null];\n} catch (err) {\n    node.error('API setup failed', err);\n    return [null, {\n        error: err.message,\n        statusCode: 500,\n        details: { stack: err.stack }\n    }];\n}", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1595, "y": 1380, "wires": [["9075e24d082cc7fd"], ["e365680d914291ba"]], "l": false, "info": "# Get MWEdge Source\n\n## Purpose\nRetrieve details for **a specific source on a specific MWEdge**.\n\n## API Details\n- **Endpoint**: `GET /api/mwedge/:_id/source/:sourceId`\n- **Authentication**: Bearer token\n- **Content-Type**: `application/json`\n\n## Required Inputs\n| Field | Location | Description |\n|-------|----------|-------------|\n| `msg.techex.baseUrlProd` | msg.techex | Base URL of the TechEx Core (e.g. `https://txcore`) |\n| `msg.techex.apiKeyProd`  | msg.techex | API/JWT token |\n| `_id` | (`msg.techex._id`, `msg._id`, etc.) | **MWEdge unique ID** |\n| `sourceId` | (`msg.techex.sourceId`, `msg.sourceId`) | **Unique source ID** |\n\n### Optional\n- `msg.method`  – Override HTTP method (default **GET**)\n- `msg.params`  – Object of query parameters to append\n\n## Outputs\n- **Output 1** – Success: full `msg` prepared for the HTTP Request node (URL, headers, etc.)\n- **Output 2** – Error object `{ error, statusCode, details }`\n\n## Error Scenarios\n- Missing configuration (**400**)\n- Missing `_id` or `sourceId` (**400**)\n- Rate-limit triggered (**429**)\n- Internal setup failure (**500**)\n\n---\n*Author: <EMAIL>*"}, {"id": "698cb44a475df783", "type": "change", "z": "77bf3dd067e362fd", "g": "925aa305e0b01b49", "name": "Set topic: sourceBackup", "rules": [{"t": "set", "p": "topic", "pt": "msg", "to": "sourceBackup", "tot": "str"}, {"t": "set", "p": "parts", "pt": "msg", "to": "{ \"id\": _groupId, \"index\": 2, \"count\": 4 }", "tot": "jsonata"}], "x": 1475, "y": 1380, "wires": [["03639636331b1f35"]], "l": false}, {"id": "7f2f8c1c3982f4b3", "type": "function", "z": "77bf3dd067e362fd", "g": "925aa305e0b01b49", "name": "HTTP → Join wrapper (with error side-output)", "func": "// Wrap HTTP response and split OK vs Error to 2 outputs\n// Output 1: normalized wrapper { ok, statusCode, data } → Join\n// Output 2: only when HTTP error (statusCode >= 400 or msg.error)\n\nconst sc = Number(msg.statusCode) || 0;\n\n// Preserve topic/parts for Join; do not touch them\nconst wrapped = {\n  ok: sc >= 200 && sc < 300,\n  statusCode: sc,\n  data: msg.payload\n};\n\nmsg.payload = wrapped;\n\n// Decide if this is an error\nconst isHttpError = !wrapped.ok || !!msg.error || sc === 0;\n\n// Optional status indicator in editor\nnode.status({\n  fill: wrapped.ok ? \"green\" : \"red\",\n  shape: wrapped.ok ? \"dot\" : \"ring\",\n  text: `HTTP ${sc || \"?\"}`\n});\n\nif (isHttpError) {\n  // Build a lightweight error copy preserving routing fields\n  const errMsg = {\n    _isHttpError: true,\n    topic: msg.topic,\n    parts: msg.parts,\n    statusCode: sc,\n    error: msg.error || null,\n    url: msg.url,\n    method: msg.method\n  };\n  return [msg, errMsg];\n}\n\nreturn [msg, null];\n", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1745, "y": 1380, "wires": [["138bea7c591acd0c", "8cee4a563b63aca9"], ["fe0573331a6b98c2"]], "l": false}, {"id": "138bea7c591acd0c", "type": "debug", "z": "77bf3dd067e362fd", "g": "925aa305e0b01b49", "name": "Debug OK: TechEx TxCore Get a Stream", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1745, "y": 1320, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "fe0573331a6b98c2", "type": "debug", "z": "77bf3dd067e362fd", "g": "925aa305e0b01b49", "name": "Debug Error: TechEx Get a Stream", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1745, "y": 1440, "wires": [], "icon": "node-red/alert.svg", "l": false}, {"id": "1e93e3484f813192", "type": "change", "z": "77bf3dd067e362fd", "g": "925aa305e0b01b49", "name": "Clean header", "rules": [{"t": "delete", "p": "method", "pt": "msg"}, {"t": "delete", "p": "headers", "pt": "msg"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 1705, "y": 1380, "wires": [["7f2f8c1c3982f4b3"]], "l": false}, {"id": "03639636331b1f35", "type": "delay", "z": "77bf3dd067e362fd", "g": "925aa305e0b01b49", "name": "", "pauseType": "delay", "timeout": "2", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 1525, "y": 1380, "wires": [["690acfc7ed67ff4e"]], "l": false}, {"id": "dbbf94e7ff1134b2", "type": "group", "z": "77bf3dd067e362fd", "name": "[005] Get a Output", "style": {"label": true}, "nodes": ["6ebc24af626b8bb6", "d3acbef790d0393c", "def5e18bf89ad650", "8a50297dddec15a2", "440779cba7e085ee", "d9049ff6d6534f6b", "13216ad70165bc46", "fa4587f0ad5c0519", "61d2fc6a026565af", "840627bd46316016"], "x": 1434, "y": 1539, "w": 382, "h": 202}, {"id": "6ebc24af626b8bb6", "type": "http request", "z": "77bf3dd067e362fd", "g": "dbbf94e7ff1134b2", "name": "", "method": "use", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1655, "y": 1640, "wires": [["d3acbef790d0393c", "61d2fc6a026565af"]], "l": false}, {"id": "d3acbef790d0393c", "type": "debug", "z": "77bf3dd067e362fd", "g": "dbbf94e7ff1134b2", "name": "Debug OK: TEXCORE Get TXEdge Output", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1655, "y": 1580, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "def5e18bf89ad650", "type": "debug", "z": "77bf3dd067e362fd", "g": "dbbf94e7ff1134b2", "name": "Debug Error: TEXCORE Get TXEdge Output", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1595, "y": 1700, "wires": [], "icon": "node-red/alert.svg", "l": false, "info": "# Flow Debug Error Get Secret\n\n## Description\nThis `Debug` node is used to log and inspect errors that occur during the secret retrieval process. It captures the full message object (`msg`) and displays the `msg.statusCode` in the node's status field for quick monitoring.\n\n## Input\n- **Trigger:**\n  - The node is triggered by the output of a preceding node in the flow, typically when an error occurs during secret retrieval.\n- **Context / Global Vars:** None required.\n- **Expected Secrets:** None required.\n\n## Output\n- **Success Path:**\n  - Logs the full incoming message (`msg`) to the debug sidebar for inspection.\n  - Displays the `msg.statusCode` in the node's status field for quick reference.\n  - Outputs the message for further processing if connected downstream.\n- **Error Path:**\n  - This node does not handle errors but is used to inspect and debug issues in the incoming message.\n\n## Notes\n> - The debug node is enabled by default to assist in troubleshooting.\n> - Use this node during development or troubleshooting to identify and resolve issues in the secret retrieval process.\n> - The `msg.statusCode` is displayed in the node's status field for quick monitoring of the HTTP response status.\n\n## Author\n[<NAME_EMAIL>](mailto:<EMAIL>)\n\n## qibb Docs\n[📚 qibb Platform Documentation](https://docs.qibb.com/platform/latest/)\n"}, {"id": "8a50297dddec15a2", "type": "function", "z": "77bf3dd067e362fd", "g": "dbbf94e7ff1134b2", "name": "Get MWEdge Output", "func": "// ------------------------------------------------------------\n// Get MWEdge Output – corrected to include MWEdge _id in the URL\n// ------------------------------------------------------------\n\n// 1. Extract configuration\nconst baseUrl = msg.techex?.baseUrlProd;\nconst token   = msg.techex?.apiKeyProd;\n\n// 2. Validate configuration\nif (!baseUrl || !token) {\n    return [null, {\n        error: \"Missing configuration: baseUrlProd or apiKeyProd\",\n        statusCode: 400,\n        details: { hasBaseUrl: !!baseUrl, hasToken: !!token }\n    }];\n}\n\n// 3. Validate path parameters\nconst mwedgeId = msg.techex.edgeId;\nconst outputId = msg.techex.outputId;\n\nif (!mwedgeId || !outputId) {\n    return [null, {\n        error: \"Missing path parameter(s): _id and/or outputId\",\n        statusCode: 400,\n        details: { hasMwedgeId: !!mwedgeId, hasOutputId: !!outputId }\n    }];\n}\n\n// 4. Simple rate-limit (1 request/s per flow)\nconst opKey = 'rateLimit.GetMWEdgeOutput';\nconst last  = flow.get(opKey) || 0;\nconst MIN_INTERVAL = 10; // ms\nif (Date.now() - last < MIN_INTERVAL) {\n    return [null, {\n        error: \"Rate limit: Too many requests\",\n        statusCode: 429,\n        retryAfter: MIN_INTERVAL - (Date.now() - last)\n    }];\n}\nflow.set(opKey, Date.now());\n\n// 5. Build request\ntry {\n    // 👉  Correct URL: /api/mwedge/:_id/output/:outputId\n    msg.url = baseUrl.replace(/\\/$/, '') +\n              '/api/mwedge/' + encodeURIComponent(mwedgeId) +\n              '/output/'     + encodeURIComponent(outputId);\n\n    msg.method = msg.method || 'GET';\n\n    // Optional query-string support\n    if (msg.params && typeof msg.params === 'object') {\n        const query = Object.entries(msg.params)\n            .map(([k, v]) => `${encodeURIComponent(k)}=${encodeURIComponent(v)}`)\n            .join('&');\n        if (query) msg.url += '?' + query;\n    }\n\n    // Headers\n    msg.headers = {\n        ...(msg.headers || {}),\n        Authorization: `Bearer ${token}`,\n        Accept: 'application/json',\n        'Content-Type': 'application/json'\n    };\n\n    // Debug status\n    node.status({ fill: 'green', shape: 'dot', text: `${msg.method}` });\n    // msg.statusCode = 200;\n    return [msg, null];\n\n} catch (err) {\n    node.error('API setup failed', err);\n    return [null, {\n        error: err.message,\n        statusCode: 500,\n        details: { stack: err.stack }\n    }];\n}\n", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1595, "y": 1640, "wires": [["6ebc24af626b8bb6"], ["def5e18bf89ad650"]], "l": false, "info": "# Get MWEdge Output\n\nRetrieve a single output on a specific MWEdge.\n\n- **Endpoint**: `GET /api/mwedge/:_id/output/:outputId`\n- **Required path params**: `_id` (MWEdge ID), `outputId` (output UID)\n- **Auth**: Bearer token ( `msg.techex.apiKeyProd` )\n"}, {"id": "440779cba7e085ee", "type": "change", "z": "77bf3dd067e362fd", "g": "dbbf94e7ff1134b2", "name": "Set topic: output", "rules": [{"t": "set", "p": "topic", "pt": "msg", "to": "output", "tot": "str"}, {"t": "set", "p": "parts", "pt": "msg", "to": "{ \"id\": _groupId, \"index\": 3, \"count\": 4 }", "tot": "jsonata"}], "x": 1475, "y": 1640, "wires": [["840627bd46316016"]], "l": false}, {"id": "d9049ff6d6534f6b", "type": "function", "z": "77bf3dd067e362fd", "g": "dbbf94e7ff1134b2", "name": "HTTP → Join wrapper (with error side-output)", "func": "// Wrap HTTP response and split OK vs Error to 2 outputs\n// Output 1: normalized wrapper { ok, statusCode, data } → Join\n// Output 2: only when HTTP error (statusCode >= 400 or msg.error)\n\nconst sc = Number(msg.statusCode) || 0;\n\n// Preserve topic/parts for Join; do not touch them\nconst wrapped = {\n  ok: sc >= 200 && sc < 300,\n  statusCode: sc,\n  data: msg.payload\n};\n\nmsg.payload = wrapped;\n\n// Decide if this is an error\nconst isHttpError = !wrapped.ok || !!msg.error || sc === 0;\n\n// Optional status indicator in editor\nnode.status({\n  fill: wrapped.ok ? \"green\" : \"red\",\n  shape: wrapped.ok ? \"dot\" : \"ring\",\n  text: `HTTP ${sc || \"?\"}`\n});\n\nif (isHttpError) {\n  // Build a lightweight error copy preserving routing fields\n  const errMsg = {\n    _isHttpError: true,\n    topic: msg.topic,\n    parts: msg.parts,\n    statusCode: sc,\n    error: msg.error || null,\n    url: msg.url,\n    method: msg.method\n  };\n  return [msg, errMsg];\n}\n\nreturn [msg, null];\n", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1755, "y": 1640, "wires": [["fa4587f0ad5c0519", "8cee4a563b63aca9"], ["13216ad70165bc46"]], "l": false}, {"id": "13216ad70165bc46", "type": "debug", "z": "77bf3dd067e362fd", "g": "dbbf94e7ff1134b2", "name": "Debug Error: TechEx Get a Stream", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1755, "y": 1700, "wires": [], "icon": "node-red/alert.svg", "l": false}, {"id": "fa4587f0ad5c0519", "type": "debug", "z": "77bf3dd067e362fd", "g": "dbbf94e7ff1134b2", "name": "Debug OK: TechEx TxCore Get a Stream", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1755, "y": 1580, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "61d2fc6a026565af", "type": "change", "z": "77bf3dd067e362fd", "g": "dbbf94e7ff1134b2", "name": "Clean header", "rules": [{"t": "delete", "p": "method", "pt": "msg"}, {"t": "delete", "p": "headers", "pt": "msg"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 1705, "y": 1640, "wires": [["d9049ff6d6534f6b"]], "l": false}, {"id": "840627bd46316016", "type": "delay", "z": "77bf3dd067e362fd", "g": "dbbf94e7ff1134b2", "name": "", "pauseType": "delay", "timeout": "3", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 1525, "y": 1640, "wires": [["8a50297dddec15a2"]], "l": false}, {"id": "be9df67fda293123", "type": "group", "z": "77bf3dd067e362fd", "name": "006 - Join <PERSON> Query", "style": {"label": true}, "nodes": ["8cee4a563b63aca9", "300004dd86d12c81"], "x": 2044, "y": 739, "w": 173, "h": 82}, {"id": "8cee4a563b63aca9", "type": "join", "z": "77bf3dd067e362fd", "g": "be9df67fda293123", "name": "Join 4 branches (by topic, per groupId)", "mode": "manual", "build": "object", "property": "payload", "propertyType": "msg", "key": "topic", "joiner": "\\n", "joinerType": "str", "useparts": true, "accumulate": false, "timeout": "10", "count": "4", "reduceRight": false, "reduceExp": "", "reduceInit": "", "reduceInitType": "", "reduceFixup": "", "x": 2085, "y": 780, "wires": [["300004dd86d12c81"]], "l": false}, {"id": "300004dd86d12c81", "type": "function", "z": "77bf3dd067e362fd", "g": "be9df67fda293123", "name": "Gate & retry on 429", "func": "// Expect: msg.payload = { stream, sourceMain, sourceBackup, output }\n// Each is { ok, statusCode, data }\nconst p = msg.payload || {};\nconst keys = [\"stream\",\"sourceMain\",\"sourceBackup\",\"output\"];\n\n// Incomplete join? drop and retry\nif (keys.some(k => !(k in p))) {\n  node.status({fill:\"grey\", shape:\"ring\", text:\"incomplete join → retry\"});\n  return [null, msg];\n}\n\nconst codes = keys.map(k => Number(p[k]?.statusCode || 0));\nconst any429 = codes.some(c => c === 429);\nconst anyHardErr = keys.some(k => p[k] && p[k].ok === false && p[k].statusCode >= 400);\n\nif (any429) {\n  node.status({fill:\"yellow\", shape:\"ring\", text:\"rate limited (429) – retry 10s\"});\n  msg.topic = \"warning\";\n  msg._reason = \"429\";\n  return [null, msg];\n}\n\nif (anyHardErr) {\n  node.status({fill:\"red\", shape:\"dot\", text:\"one or more API errors\"});\n  // Still pass through so UI can show last known state if you map for it\n  return [msg, null];\n}\n\nnode.status({fill:\"green\", shape:\"dot\", text:\"OK\"});\nreturn [msg, null];\n", "outputs": 2, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 2135, "y": 780, "wires": [["123c83916194e5cc"], ["f9bd4bbd790541a1"]], "l": false}, {"id": "4d69a20e58782449", "type": "group", "z": "77bf3dd067e362fd", "name": "009 - Rate Limit Handler", "style": {"label": true}, "nodes": ["f9bd4bbd790541a1", "c86e58662358833f", "d3e9c57c92c10bce", "4e6edee4195cdf97"], "x": 2054, "y": 959, "w": 272, "h": 82}, {"id": "f9bd4bbd790541a1", "type": "change", "z": "77bf3dd067e362fd", "g": "4d69a20e58782449", "name": "Reset group & clean msg for retry", "rules": [{"t": "delete", "p": "parts", "pt": "msg"}, {"t": "delete", "p": "topic", "pt": "msg"}, {"t": "delete", "p": "payload", "pt": "msg"}, {"t": "set", "p": "_retry", "pt": "msg", "to": "429", "tot": "str"}], "x": 2095, "y": 1000, "wires": [["c86e58662358833f"]], "l": false}, {"id": "c86e58662358833f", "type": "delay", "z": "77bf3dd067e362fd", "g": "4d69a20e58782449", "name": "Retry after 10s", "pauseType": "delay", "timeout": "10", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 2155, "y": 1000, "wires": [["4e6edee4195cdf97"]], "l": false}, {"id": "d3e9c57c92c10bce", "type": "link out", "z": "77bf3dd067e362fd", "g": "4d69a20e58782449", "name": "Retry after error", "mode": "link", "links": ["b84d818dc7114d95"], "x": 2285, "y": 1000, "wires": []}, {"id": "4e6edee4195cdf97", "type": "change", "z": "77bf3dd067e362fd", "g": "4d69a20e58782449", "name": "Clean all", "rules": [{"t": "delete", "p": "_msgid", "pt": "msg"}, {"t": "delete", "p": "_groupId", "pt": "msg"}, {"t": "delete", "p": "techex", "pt": "msg"}, {"t": "delete", "p": "method", "pt": "msg"}, {"t": "delete", "p": "topic", "pt": "msg"}, {"t": "delete", "p": "url", "pt": "msg"}, {"t": "delete", "p": "headers", "pt": "msg"}, {"t": "delete", "p": "statusCode", "pt": "msg"}, {"t": "delete", "p": "payload", "pt": "msg"}, {"t": "delete", "p": "redirectList", "pt": "msg"}, {"t": "delete", "p": "retry", "pt": "msg"}, {"t": "delete", "p": "responseCookies", "pt": "msg"}, {"t": "delete", "p": "originalStream", "pt": "msg"}], "x": 2215, "y": 1000, "wires": [["d3e9c57c92c10bce"]], "l": false}, {"id": "0ad3470ee388561d", "type": "group", "z": "77bf3dd067e362fd", "name": "[002] Get a Stream", "style": {"label": true}, "nodes": ["84eb1ce1f39720e7", "342ecae257a35fbe", "8142a8f81e0c3932", "c48871a9eba48bef", "cf76da4a652442a8", "4aa229ea6abfb706", "c481720600fe7203", "26c461ed6645ba4e", "2cba34321d3a62df"], "x": 1434, "y": 719, "w": 392, "h": 202}, {"id": "84eb1ce1f39720e7", "type": "debug", "z": "77bf3dd067e362fd", "g": "0ad3470ee388561d", "name": "Debug OK: TechEx TxCore Get a Stream", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1615, "y": 760, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "342ecae257a35fbe", "type": "debug", "z": "77bf3dd067e362fd", "g": "0ad3470ee388561d", "name": "Debug Error: TechEx Get a Stream", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1545, "y": 880, "wires": [], "icon": "node-red/alert.svg", "l": false}, {"id": "8142a8f81e0c3932", "type": "change", "z": "77bf3dd067e362fd", "g": "0ad3470ee388561d", "name": "Set topic: stream", "rules": [{"t": "set", "p": "topic", "pt": "msg", "to": "stream", "tot": "str"}, {"t": "set", "p": "parts", "pt": "msg", "to": "{ \"id\": _groupId, \"index\": 0, \"count\": 4 }", "tot": "jsonata"}], "x": 1475, "y": 820, "wires": [["2cba34321d3a62df"]], "l": false}, {"id": "c48871a9eba48bef", "type": "function", "z": "77bf3dd067e362fd", "g": "0ad3470ee388561d", "name": "HTTP → Join wrapper (with error side-output)", "func": "// Wrap HTTP response and split OK vs Error to 2 outputs\n// Output 1: normalized wrapper { ok, statusCode, data } → Join\n// Output 2: only when HTTP error (statusCode >= 400 or msg.error)\n\nconst sc = Number(msg.statusCode) || 0;\n\n// <PERSON>le missing payload or a non-object payload gracefully\nconst data = (typeof msg.payload === 'object' && msg.payload !== null) ? msg.payload : {};\n\n// Preserve topic/parts for <PERSON><PERSON>; do not touch them\nconst wrapped = {\n\tok: sc >= 200 && sc < 300,\n\tstatusCode: sc,\n\tdata: data,\n\terror: msg.error || (sc === 0 ? \"Connection refused or timed out\" : null)\n};\n\nmsg.payload = wrapped;\n\n// Decide if this is an error\nconst isHttpError = !wrapped.ok || !!msg.error || sc === 0;\n\n// Optional status indicator in editor\nnode.status({\n\tfill: wrapped.ok ? \"green\" : \"red\",\n\tshape: wrapped.ok ? \"dot\" : \"ring\",\n\ttext: `HTTP ${sc || \"?\"}`\n});\n\nif (isHttpError) {\n\t// Build a lightweight error copy preserving routing fields\n\tconst errMsg = {\n\t\t_isHttpError: true,\n\t\ttopic: msg.topic,\n\t\tparts: msg.parts,\n\t\tstatusCode: sc,\n\t\terror: msg.error || wrapped.error,\n\t\turl: msg.url,\n\t\tmethod: msg.method\n\t};\n\treturn [null, errMsg];\n}\n\nreturn [msg, null];\n", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1765, "y": 820, "wires": [["4aa229ea6abfb706", "8cee4a563b63aca9"], ["cf76da4a652442a8"]], "l": false}, {"id": "cf76da4a652442a8", "type": "debug", "z": "77bf3dd067e362fd", "g": "0ad3470ee388561d", "name": "Debug Error: TechEx Get a Stream", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1765, "y": 880, "wires": [], "icon": "node-red/alert.svg", "l": false}, {"id": "4aa229ea6abfb706", "type": "debug", "z": "77bf3dd067e362fd", "g": "0ad3470ee388561d", "name": "Debug OK: TechEx TxCore Get a Stream", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1765, "y": 760, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "c481720600fe7203", "type": "http request", "z": "77bf3dd067e362fd", "g": "0ad3470ee388561d", "name": "", "method": "use", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1625, "y": 820, "wires": [["84eb1ce1f39720e7", "26c461ed6645ba4e"]], "l": false}, {"id": "26c461ed6645ba4e", "type": "change", "z": "77bf3dd067e362fd", "g": "0ad3470ee388561d", "name": "Clean header", "rules": [{"t": "delete", "p": "method", "pt": "msg"}, {"t": "delete", "p": "headers", "pt": "msg"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 1675, "y": 820, "wires": [["c48871a9eba48bef"]], "l": false}, {"id": "2cba34321d3a62df", "type": "function", "z": "77bf3dd067e362fd", "g": "0ad3470ee388561d", "name": "Get a stream", "func": "// Extract configuration\nconst baseUrlProd = msg.techex?.baseUrlProd || msg.baseUrlProd;\nconst apiKeyProd = msg.techex?.apiKeyProd || msg.apiKeyProd;\n\n// Validate required fields\nif (!baseUrlProd) {\n    return [null, { \n        error: \"Missing configuration: baseUrlProd is required\", \n        statusCode: 400,\n        details: { hasBaseUrlProd: !!baseUrlProd, hasApiKeyProd: !!apiKeyProd }\n    }];\n}\n\n// Check for authentication\nif (!apiKeyProd) {\n    return [null, { \n        error: \"Missing authentication: apiKeyProd is required\", \n        statusCode: 401,\n        details: { hasBaseUrlProd: !!baseUrlProd, hasApiKeyProd: !!apiKeyProd }\n    }];\n}\n\n// Validate path parameters\nconst edgeId = msg.techex?.edgeId || msg.edgeId;\nconst streamId = msg.techex?.streamId || msg.streamId;\n\nif (!edgeId) {\n    return [null, {\n        error: \"Missing required parameter: edgeId\",\n        statusCode: 400,\n        details: { requiredParams: [\"edgeId\", \"streamId\"] }\n    }];\n}\n\nif (!streamId) {\n    return [null, {\n        error: \"Missing required parameter: streamId\",\n        statusCode: 400,\n        details: { requiredParams: [\"edgeId\", \"streamId\"] }\n    }];\n}\n\n// Rate limiting for stream operations\nconst rlKey = `rateLimit.getStream`;\nconst last = flow.get(rlKey) || 0;\nconst MIN_INTERVAL = 10; // ms\nif (Date.now() - last < MIN_INTERVAL) {\n    const retryAfter = MIN_INTERVAL - (Date.now() - last);\n    return [null, { \n        error: \"Rate limit: Too many requests\", \n        statusCode: 429, \n        details: {},\n        retryAfter \n    }];\n}\nflow.set(rlKey, Date.now());\n\n// Build request\ntry {\n    // Construct URL with path parameters\n    msg.url = baseUrlProd.replace(/\\/$/, '') + '/api/mwedge/' + encodeURIComponent(edgeId) + '/stream/' + encodeURIComponent(streamId);\n    \n    msg.method = msg.method || 'GET';\n    \n    // Set headers\n    msg.headers = { \n        ...(msg.headers || {}),\n        Authorization: `Bearer ${apiKeyProd}`,\n        Accept: 'application/json',\n        'Content-Type': 'application/json'\n    };\n    \n    // Set node status\n    node.status({ \n        fill: 'green', \n        shape: 'dot', \n        text: `${msg.method}` \n    });\n    \n    // Set success status code\n    // msg.statusCode = 200;\n    \n    return [msg, null];\n} catch (err) {\n    node.error('API setup failed', err);\n    return [null, { \n        error: err.message, \n        statusCode: 500, \n        details: { stack: err.stack } \n    }];\n}", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1535, "y": 820, "wires": [["c481720600fe7203"], ["342ecae257a35fbe"]], "inputLabels": ["trigger"], "outputLabels": ["success", "error"], "l": false, "info": "# TechEx Core Analytics Post Analytics\\n\\nThis function node prepares API requests for posting analytics data to the TechEx Core Analytics API.\\n\\n## API Details\\n- **Endpoint**: POST https://tx.core.techex.co.uk/analytics\\n- **Purpose**: Post analytics data to TechEx Core\\n- **Authentication**: Bearer token via API key\\n\\n## Required Configuration\\n- `msg.techex.baseUrlProd`: TechEx Core base URL\\n- `msg.techex.apiKeyProd`: API authentication key\\n- `msg.techex.edgeId`: Edge device identifier\\n- `msg.techex.streamId`: Stream identifier\\n\\n## Input\\n- `msg.payload`: Analytics data object (optional, defaults will be created)\\n\\n## Output\\n- **Output 1**: Configured HTTP request message for success path\\n- **Output 2**: Error message for error handling\\n\\n## Default Analytics Payload\\nIf no payload is provided, creates default analytics data with:\\n- edgeId and streamId from configuration\\n- Current timestamp\\n- Event type and metadata\\n\\n## Error Handling\\n- Validates all required configuration parameters\\n- Returns detailed error messages for missing configuration\\n- Includes request metadata for debugging\\n\\n## Request Configuration\\n- Sets appropriate headers including Authorization\\n- Configures 30-second timeout\\n- Adds request metadata for tracking"}, {"id": "12a9011b9153e4c3", "type": "group", "z": "77bf3dd067e362fd", "name": "007 - Update TXEdge Status", "style": {"label": true}, "nodes": ["2e0fd32b8111fe0e", "4b19fcf924eec65d", "123c83916194e5cc", "60a2ae6be919ef47", "fc720da720660258", "3a67ca9094051cce"], "x": 2434, "y": 739, "w": 502, "h": 142}, {"id": "2e0fd32b8111fe0e", "type": "debug", "z": "77bf3dd067e362fd", "g": "12a9011b9153e4c3", "name": "Debug OK: UI Update", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "currentSource", "statusType": "msg", "x": 2475, "y": 780, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "4b19fcf924eec65d", "type": "debug", "z": "77bf3dd067e362fd", "g": "12a9011b9153e4c3", "name": "Debug OK: UI data", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 2775, "y": 780, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "123c83916194e5cc", "type": "function", "z": "77bf3dd067e362fd", "g": "12a9011b9153e4c3", "name": "Map → TXEdge Status model", "func": "// Map the joined responses into a simple UI model, unwrapping the\n// { ok, statusCode, data } envelope each branch produces.\n\nconst j = msg.payload || {};\n\n// Safely unwrap the envelope we created after each HTTP request\nfunction unwrap(x) {\n  if (x && typeof x === 'object' && 'data' in x) return x.data || {};\n  return x || {};\n}\n\nfunction nameOf(o, fallback) {\n  if (!o || typeof o !== 'object') return fallback;\n  return o.name || o.displayName || o.label || fallback;\n}\n\nfunction isActive(o) {\n  if (!o || typeof o !== 'object') return false;\n  if (typeof o.active === 'boolean') return o.active;\n  if (typeof o.isActive === 'boolean') return o.isActive;\n  if (typeof o.online === 'boolean') return o.online;\n  // Sometimes APIs use strings for state\n  if (typeof o.status === 'string') return /up|on|online|active/i.test(o.status);\n  return false;\n}\n\nfunction countState(arr) {\n  const res = { errors: 0, warnings: 0 };\n  if (!Array.isArray(arr)) return res;\n  for (const s of arr) {\n    if (!s || s.active === false) continue; // only count active issues\n    if (s.error)    res.errors++;\n    if (s.warning) res.warnings++;\n  }\n  return res;\n}\n\n// Unwrap all four branches from the Join\nconst stream         = unwrap(j.stream);\nconst sourceMain   = unwrap(j.sourceMain);\nconst sourceBackup = unwrap(j.sourceBackup);\nconst output       = unwrap(j.output);\n\nconst model = {\n  configuredStreams:   [ { name: nameOf(stream,         \"--\") } ],\n  configuredSources:   [\n    { name: nameOf(sourceMain,   \"Input A\"), active: isActive(sourceMain) },\n    { name: nameOf(sourceBackup, \"Input B\"), active: isActive(sourceBackup) }\n  ],\n  configuredOutputs:   [ { name: nameOf(output,         \"--\") } ],\n  // Overall online if any part reports active/online\n  online: [sourceMain, sourceBackup, stream, output].some(isActive),\n  // Optional quick counts of active errors/warnings\n  summary: {\n    sourceMain:    countState(sourceMain.state),\n    sourceBackup: countState(sourceBackup.state),\n    output:        countState(output.state)\n  }\n};\n\nmsg.payload = model;\n\n// Add a new property to the message to explicitly control the UI toggle\nif (isActive(sourceMain)) {\n    msg.currentSource = 'main';\n} else if (isActive(sourceBackup)) {\n    msg.currentSource = 'backup';\n} else {\n    msg.currentSource = null;\n}\n\nnode.status({ fill: \"green\", shape: \"dot\", text: \"mapped → ui model\" });\nreturn msg;\n", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 2475, "y": 840, "wires": [["2e0fd32b8111fe0e", "3a67ca9094051cce"]], "l": false}, {"id": "60a2ae6be919ef47", "type": "function", "z": "77bf3dd067e362fd", "g": "12a9011b9153e4c3", "name": "Detect Source Change", "func": "// Get the current source from the incoming message\nconst newSource = msg.currentSource;\n\n// Get the previous source state from flow context. If it doesn't exist, set it to an initial value.\nlet oldSource = flow.get('current_source_state');\nif (oldSource === undefined) {\n    oldSource = null;\n}\n\n// Compare the current state with the previous state\nif (newSource !== oldSource) {\n    // A change has been detected.\n    \n    // Store the new state in flow context for the next comparison.\n    flow.set('current_source_state', newSource);\n    \n    // Create a new message with the change details.\n    msg.payload = newSource;\n    msg.change = `Source changed from ${oldSource || 'initial'} to ${newSource}`;\n\n    // Return the message to the next node in the flow.\n    return msg;\n}\n\n// If there's no change, return null to block the message from passing.\nreturn null;\n", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 2875, "y": 840, "wires": [["fc720da720660258", "521db1c3df2fb0db"]], "l": false}, {"id": "fc720da720660258", "type": "debug", "z": "77bf3dd067e362fd", "g": "12a9011b9153e4c3", "name": "Debug OK: Switching", "active": false, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 2875, "y": 780, "wires": [], "l": false}, {"id": "3a67ca9094051cce", "type": "ui_template", "z": "77bf3dd067e362fd", "g": "12a9011b9153e4c3", "group": "3cfbd8f881a99c1c", "name": "Control & Status (Instance B)", "order": 1, "width": "8", "height": "3", "format": "<style>\n    .status-panel {\n        background-color: #1f2937;\n        color: #f9fafb;\n        font-family: 'Segoe UI', sans-serif;\n        border: 1px solid #374151;\n        border-radius: 6px;\n        padding: 6px;\n        box-sizing: border-box;\n        width: 100%;\n        height: 100%;\n        display: flex;\n        flex-direction: column;\n        gap: 4px;\n    }\n\n    .header {\n        font-weight: 600;\n        font-size: 10px;\n        color: #aab8d0;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 2px;\n    }\n    \n    .header-left {\n        display: flex;\n        align-items: center;\n        gap: 6px;\n    }\n\n    .header-right {\n        display: flex;\n        align-items: center;\n        gap: 2px;\n    }\n\n    .lock-button {\n        cursor: pointer;\n        font-size: 11px;\n        transition: color 0.3s ease;\n        color: #ef4444; /* Locked color */\n    }\n\n    .lock-button.unlocked {\n        color: #10b981; /* Unlocked color */\n    }\n\n    .edge-info-link {\n        color: #aab8d0;\n        text-decoration: none;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 14px;\n        height: 14px;\n        font-size: 9px;\n        border: 1px solid #aab8d0;\n        border-radius: 50%;\n    }\n    .edge-info-link:hover {\n        color: #fff;\n        border-color: #fff;\n    }\n\n    .switch-box {\n        position: relative;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        height: 20px;\n        background-color: #3e4c6d;\n        border-radius: 999px;\n        border: 1px solid #4d5a77;\n        padding: 0 2px;\n        overflow: hidden;\n        margin-bottom: 4px;\n    }\n\n    .switch-btn {\n        width: 55px;\n        height: 16px;\n        font-size: 9px;\n        font-weight: 500;\n        background: transparent;\n        border: none;\n        color: #c3cee3;\n        cursor: not-allowed;\n        z-index: 1;\n        transition: color 0.3s ease, opacity 0.3s ease;\n        border-radius: 999px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding: 0;\n        opacity: 0.5;\n    }\n\n    .switch-btn.unlocked {\n        cursor: pointer;\n        opacity: 1;\n    }\n    \n    .switch-btn.active {\n        color: #ffffff;\n    }\n\n    .switch-indicator {\n        position: absolute;\n        top: 2px;\n        bottom: 2px;\n        left: 2px;\n        width: 55px;\n        background: #5c6b8b;\n        border-radius: 999px;\n        z-index: 0;\n        transition: left 0.3s ease;\n    }\n\n    .details-row {\n        display: flex;\n        align-items: center;\n        font-size: 9px;\n        margin-bottom: 1px;\n        gap: 6px;\n    }\n    \n    .details-label {\n        font-weight: 600;\n        color: #a5b4fc;\n        white-space: nowrap;\n        flex-shrink: 0;\n    }\n    \n    .details-value {\n        font-weight: 500;\n        color: #e5e7eb;\n        overflow: hidden;\n        white-space: nowrap;\n        text-overflow: ellipsis;\n        flex-grow: 1;\n    }\n\n    .value-with-tag {\n        display: flex;\n        align-items: center;\n        gap: 4px;\n        overflow: hidden;\n        flex-grow: 1;\n    }\n\n    .tag {\n        font-size: 8px;\n        padding: 1px 4px;\n        border-radius: 2px;\n        flex-shrink: 0;\n    }\n    .tag.active-fill {\n        background-color: #10b98133;\n        color: #10b981;\n    }\n    .tag.warning-fill {\n        background-color: #f59e0b33;\n        color: #f59e0b;\n    }\n    .last-updated {\n        font-size: 8px;\n        color: #9ca3af;\n    }\n\n    /* Toast Notification Styles */\n    .toast-container {\n        position: fixed;\n        top: 15px;\n        right: 15px;\n        z-index: 9999;\n        pointer-events: none;\n    }\n\n    .toast {\n        background: #374151;\n        border: 1px solid #4b5563;\n        border-radius: 6px;\n        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);\n        color: #ffffff;\n        font-family: 'Segoe UI', sans-serif;\n        font-size: 11px;\n        margin-bottom: 8px;\n        max-width: 300px;\n        min-width: 250px;\n        opacity: 0;\n        padding: 8px 10px;\n        pointer-events: all;\n        position: relative;\n        transform: translateX(100%);\n        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    }\n\n    .toast.show {\n        opacity: 1;\n        transform: translateX(0);\n    }\n\n    .toast.success {\n        border-left: 3px solid #10b981;\n    }\n\n    .toast.error {\n        border-left: 3px solid #ef4444;\n    }\n\n    .toast.warning {\n        border-left: 3px solid #f59e0b;\n    }\n\n    .toast.info {\n        border-left: 3px solid #3b82f6;\n    }\n\n    .toast-header {\n        display: flex;\n        align-items: center;\n        gap: 6px;\n        margin-bottom: 3px;\n    }\n\n    .toast-icon {\n        width: 14px;\n        height: 14px;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 10px;\n        font-weight: bold;\n        flex-shrink: 0;\n    }\n\n    .toast-icon.success {\n        background: #10b981;\n    }\n\n    .toast-icon.error {\n        background: #ef4444;\n    }\n\n    .toast-icon.warning {\n        background: #f59e0b;\n    }\n\n    .toast-icon.info {\n        background: #3b82f6;\n    }\n\n    .toast-title {\n        font-weight: 600;\n        font-size: 11px;\n        color: #ffffff;\n    }\n\n    .toast-close {\n        background: none;\n        border: none;\n        color: #9ca3af;\n        cursor: pointer;\n        font-size: 12px;\n        margin-left: auto;\n        padding: 0;\n    }\n\n    .toast-message {\n        color: #d1d5db;\n        font-size: 10px;\n        line-height: 1.2;\n        margin: 0;\n    }\n\n    .toast-timestamp {\n        color: #9ca3af;\n        font-size: 9px;\n        margin-top: 1px;\n    }\n\n    .toast-progress {\n        position: absolute;\n        bottom: 0;\n        left: 0;\n        height: 1px;\n        background: rgba(255, 255, 255, 0.3);\n        border-radius: 0 0 6px 6px;\n        transition: width linear;\n    }\n\n    .toast-progress.success {\n        background: #10b981;\n    }\n\n    .toast-progress.error {\n        background: #ef4444;\n    }\n\n    .toast-progress.warning {\n        background: #f59e0b;\n    }\n\n    .toast-progress.info {\n        background: #3b82f6;\n    }\n</style>\n\n<div class=\"status-panel\">\n    <div class=\"header\">\n        <div class=\"header-left\">\n            <span id=\"lock-button\" class=\"lock-button\">🔒</span>\n            <span>Status</span>\n        </div>\n        <div class=\"header-right\">\n            <a id=\"edge-config-link\" class=\"edge-info-link\" target=\"_blank\">ℹ️</a>\n            <span class=\"last-updated\" id=\"last-updated\">--</span>\n        </div>\n    </div>\n    \n    <div class=\"switch-box\">\n        <button id=\"btn-main\" class=\"switch-btn\">Main</button>\n        <button id=\"btn-backup\" class=\"switch-btn\">Backup</button>\n        <span id=\"switch-indicator\" class=\"switch-indicator\"></span>\n    </div>\n\n    <div class=\"details-row\">\n        <div class=\"details-label\">Stream:</div>\n        <div class=\"details-value\" id=\"stream-name\">--</div>\n    </div>\n    <div class=\"details-row\">\n        <div class=\"details-label\">Source:</div>\n        <div class=\"value-with-tag\">\n            <span class=\"details-value\" id=\"source-name\">--</span>\n            <span id=\"source-status-display\"></span>\n        </div>\n    </div>\n    <div class=\"details-row\">\n        <div class=\"details-label\">Output:</div>\n        <div class=\"details-value\" id=\"output-name\">--</div>\n    </div>\n</div>\n\n<div class=\"toast-container\" id=\"toastContainer\"></div>\n\n<script>\n(function(scope) {\n    const mainBtn = document.getElementById(\"btn-main\");\n    const backupBtn = document.getElementById(\"btn-backup\");\n    const lockButtonEl = document.getElementById(\"lock-button\");\n    const streamNameEl = document.getElementById(\"stream-name\");\n    const sourceNameEl = document.getElementById(\"source-name\");\n    const sourceStatusDisplayEl = document.getElementById(\"source-status-display\");\n    const outputNameEl = document.getElementById(\"output-name\");\n    const updatedEl = document.getElementById(\"last-updated\");\n    const edgeConfigLinkEl = document.getElementById(\"edge-config-link\");\n    const indicator = document.getElementById(\"switch-indicator\");\n\n    let initialized = false;\n    let isLocked = true;\n\n    function disableButtons() {\n        mainBtn.disabled = true;\n        backupBtn.disabled = true;\n        mainBtn.classList.remove('unlocked');\n        backupBtn.classList.remove('unlocked');\n        mainBtn.style.opacity = 0.5;\n        backupBtn.style.opacity = 0.5;\n        mainBtn.style.cursor = \"not-allowed\";\n        backupBtn.style.cursor = \"not-allowed\";\n    }\n\n    function enableButtons() {\n        mainBtn.disabled = false;\n        backupBtn.disabled = false;\n        mainBtn.classList.add('unlocked');\n        backupBtn.classList.add('unlocked');\n        mainBtn.style.opacity = 1;\n        backupBtn.style.opacity = 1;\n        mainBtn.style.cursor = \"pointer\";\n        backupBtn.style.cursor = \"pointer\";\n    }\n\n    function toggleLock() {\n        isLocked = !isLocked;\n        if (isLocked) {\n            lockButtonEl.textContent = '🔒';\n            lockButtonEl.classList.remove('unlocked');\n            disableButtons();\n        } else {\n            lockButtonEl.textContent = '🔓';\n            lockButtonEl.classList.add('unlocked');\n            enableButtons();\n        }\n    }\n\n    lockButtonEl.addEventListener('click', toggleLock);\n\n    function setActiveButton(name) {\n        if (!indicator) return;\n\n        mainBtn.classList.remove(\"active\");\n        backupBtn.classList.remove(\"active\");\n\n        if (name === \"main\") {\n            mainBtn.classList.add(\"active\");\n            indicator.style.left = '2px';\n        } else if (name === \"backup\") {\n            backupBtn.classList.add(\"active\");\n            indicator.style.left = 'calc(100% - 59px)';\n        } else {\n            // Default to 'Main' if no active source is explicitly found or on initialization\n            mainBtn.classList.add(\"active\");\n            indicator.style.left = '2px';\n        }\n    }\n\n    // Add event listeners to send a message back when the buttons are clicked\n    mainBtn.addEventListener('click', function() {\n        if (!isLocked) {\n            scope.send({currentSource: \"main\"});\n        }\n    });\n\n    backupBtn.addEventListener('click', function() {\n        if (!isLocked) {\n            scope.send({currentSource: \"backup\"});\n        }\n    });\n\n    disableButtons();\n\n    scope.$watch('msg', function(msg) {\n    if (!msg || !msg.payload) return;\n    \n    // Use a dedicated property for the active source toggle\n    if (msg.currentSource) {\n    setActiveButton(msg.currentSource);\n    } else {\n    // Fallback to the old logic if currentSource isn't set\n    const sources = msg.payload.configuredSources || [];\n    const activeSource = sources.find(src => src.active);\n    if (activeSource) {\n    const activeSourceName = (activeSource.name || \"\").trim().toLowerCase();\n    if (activeSourceName.includes(\"backup\") || activeSourceName.includes(\"b\")) {\n    setActiveButton(\"backup\");\n    } else if (activeSourceName.includes(\"main\") || activeSourceName.includes(\"a\")) {\n    setActiveButton(\"main\");\n    } else {\n    setActiveButton(null);\n    }\n    } else {\n    setActiveButton(null);\n    }\n    }\n    \n    updateStatusFields(msg.payload, msg.techex);\n    \n    if (!isLocked) {\n    enableButtons();\n    }\n    });\n\n    function updateStatusFields(payload, techex) {\n        const sources = payload.configuredSources || [];\n        const configuredStreams = payload.configuredStreams || [];\n        const configuredOutputs = payload.configuredOutputs || [];\n        \n        const activeSource = sources.find(src => src.active);\n        const firstOutput = configuredOutputs[0];\n        const stream = configuredStreams[0];\n\n        streamNameEl.textContent = stream?.name || \"--\";\n\n        if (activeSource) {\n            sourceNameEl.textContent = activeSource.name || \"--\";\n            sourceStatusDisplayEl.innerHTML = `<span class=\"tag active-fill\">Active</span>`;\n        } else {\n            const standbySource = sources.find(src => !src.active);\n            sourceNameEl.textContent = standbySource?.name || \"Unknown\";\n            sourceStatusDisplayEl.innerHTML = `<span class=\"tag warning-fill\">Standby</span>`;\n        }\n\n        outputNameEl.textContent = firstOutput?.name || \"--\";\n        updatedEl.textContent = new Date().toLocaleTimeString();\n        \n        // Set the link for the info icon\n        if (techex && techex.baseUrlProd && techex.edgeId) {\n            edgeConfigLinkEl.href = `${techex.baseUrlProd}/txedges/${techex.edgeId}`;\n        }\n\n        if (!initialized) {\n            initialized = true;\n            if (!isLocked) {\n                enableButtons();\n            }\n        } else if (!isLocked) {\n            enableButtons();\n        }\n    }\n\n\n    class ToastNotification {\n        constructor() {\n            this.container = document.getElementById('toastContainer');\n            this.toastId = 0;\n        }\n\n        show(message, type = 'info', duration = 5000) {\n            if (!this.container || !message) return;\n\n            const toastId = ++this.toastId;\n            const toast = this.createToast(message, type, toastId);\n            \n            this.container.appendChild(toast);\n\n            requestAnimationFrame(() => {\n                toast.classList.add('show');\n            });\n\n            if (duration > 0) {\n                this.startProgressBar(toast, duration);\n                setTimeout(() => {\n                    this.dismiss(toast);\n                }, duration);\n            }\n\n            return toastId;\n        }\n\n        createToast(message, type, id) {\n            const toast = document.createElement('div');\n            toast.className = `toast ${type}`;\n            toast.setAttribute('data-toast-id', id);\n\n            const iconMap = {\n                success: '✓',\n                error: '✕',\n                warning: '!',\n                info: 'i'\n            };\n\n            const titleMap = {\n                success: 'Success',\n                error: 'Error',\n                warning: 'Warning',\n                info: 'Info'\n            };\n\n            const timestamp = new Date().toLocaleTimeString('en-US', { hour12: false });\n\n            toast.innerHTML = `\n                <div class=\"toast-header\">\n                    <div class=\"toast-icon ${type}\">${iconMap[type] || 'i'}</div>\n                    <div class=\"toast-title\">${titleMap[type] || 'Notification'}</div>\n                    <button class=\"toast-close\" onclick=\"this.closest('.toast').remove()\">&times;</button>\n                </div>\n                <div class=\"toast-message\">${this.escapeHtml(message)}</div>\n                <div class=\"toast-timestamp\">${timestamp}</div>\n                <div class=\"toast-progress ${type}\" style=\"width: 100%\"></div>\n            `;\n\n            return toast;\n        }\n\n        startProgressBar(toast, duration) {\n            const progressBar = toast.querySelector('.toast-progress');\n            if (progressBar) {\n                progressBar.style.transition = `width ${duration}ms linear`;\n                requestAnimationFrame(() => {\n                    progressBar.style.width = '0%';\n                });\n            }\n        }\n\n        dismiss(toast) {\n            if (toast && toast.parentElement) {\n                toast.classList.remove('show');\n                setTimeout(() => {\n                    if (toast.parentElement) {\n                        toast.parentElement.removeChild(toast);\n                    }\n                }, 300);\n            }\n        }\n\n        escapeHtml(unsafe) {\n            return unsafe\n                .replace(/&/g, \"&amp;\")\n                .replace(/</g, \"&lt;\")\n                .replace(/>/g, \"&gt;\")\n                .replace(/\"/g, \"&quot;\")\n                .replace(/'/g, \"&#039;\");\n        }\n    }\n\n    const toastSystem = new ToastNotification();\n\n})(scope);\n</script>", "storeOutMessages": true, "fwdInMessages": true, "resendOnRefresh": false, "templateScope": "local", "className": "", "x": 2650, "y": 840, "wires": [["60a2ae6be919ef47", "4b19fcf924eec65d"]]}, {"id": "3cfbd8f881a99c1c", "type": "ui_group", "name": "Techex Edge Status 2", "tab": "d9a16a199933e63a", "order": 1, "disp": false, "width": "8", "collapse": false, "className": ""}, {"id": "d9a16a199933e63a", "type": "ui_tab", "name": "Channel Pipeline 2", "icon": "dashboard", "disabled": false, "hidden": true}, {"id": "8bd382fb5761ed8e", "type": "group", "z": "77bf3dd067e362fd", "name": "008 - Send switch", "style": {"label": true}, "nodes": ["4329be93cdce4ac1", "1db06beb1dcc91fe", "638f99ce9d7d113f", "ad3959344fc6bdc7", "dab587d380a58a3e", "16a44882495a176e", "09f66accaa737dab", "b66e0788b5142317", "73ebbea621e63b03", "521db1c3df2fb0db"], "x": 2984, "y": 739, "w": 722, "h": 202}, {"id": "4329be93cdce4ac1", "type": "function", "z": "77bf3dd067e362fd", "g": "8bd382fb5761ed8e", "name": "Format API Request", "func": "// Get the new source from the previous node's output\nconst newSource = msg.payload;\n\n// Log the request intent\nnode.log(`[REQ] API call to switch source to: ${newSource}`);\n\n// Check if a source has been selected\nif (newSource) {\n\n    const baseUrl = msg.techex.baseUrlProd;\n    const edgeId = msg.techex.edgeId;\n    const sourceIdMain = msg.techex.sourceIdMain;\n    const sourceIdBackup = msg.techex.sourceIdBackup;\n\n    let url = '';\n    if (newSource === 'main') {\n        url = `${baseUrl}/api/mwedge/${edgeId}/source/${sourceIdMain}`;    \n        // The backup source needs to be deactivated. The API call is structured to activate one source, so we'll need to send a separate message to deactivate the other.\n        // You can't activate both at the same time.\n\n    } else if (newSource === 'backup') {\n        url = `${baseUrl}/api/mwedge/${edgeId}/source/${sourceIdBackup}`;\n        // Same logic applies here: deactivating the main source must be a separate step.\n    }\n\n    // Create the message for the API call to activate the selected source\n    msg.url = url;\n    msg.method = 'PUT';\n    msg.headers = {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n        'Authorization': `Bearer ${global.get(\"SECRETS.TECHEX_TXCORE_API_KEY_PROD\")}`\n    };\n    msg.payload = { active: true };\n\n    // Send the message to the HTTP Request node to activate the selected source.\n    return msg;\n} else {\n    // If no new source is detected (e.g., from an initial message or error), block the flow.\n    return null;\n}\n\n", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 3335, "y": 840, "wires": [["1db06beb1dcc91fe"]], "l": false, "info": "# Format API Request\n\n## Purpose\nThis function formats an API request to switch the stream status to either 'main' or 'backup'.\n\n## Input\n- `msg.payload`: A string indicating the desired stream type (`'main'` or `'backup'`).\n\n## Output\n- The `msg` object is updated with the following properties:\n  - `msg.url`: The API endpoint URL for the selected stream type.\n  - `msg.method`: HTTP method set to `PUT`.\n  - `msg.headers`: Includes `Accept` and `Authorization` headers.\n  - `msg.payload`: JSON payload to activate the stream.\n  - `msg.originalStream`: The original stream type for context.\n\n## Logic\n1. Retrieve the stream type (`'main'` or `'backup'`) from `msg.payload`.\n2. Log the request intent.\n3. Construct the appropriate API endpoint URL based on the stream type using global secrets.\n4. Set the HTTP method to `PUT`.\n5. Add headers, including an authorization token retrieved from global secrets.\n6. Define the payload to activate the stream.\n7. Attach the original stream type to the `msg` object for context.\n8. Return the updated `msg` object.\n\n## Notes\n> Ensure that the required global secrets (`SECRETS.TECHEX_TXCORE_URL_PROD`, `SECRETS.TECHEX_TXEDGE_MWEDGE_ID_PROD`, `SECRETS.TECHEX_TXEDGE_BACKUP_SOURCE_ID`, `SECRETS.TECHEX_TXEDGE_MAIN_SOURCE_ID`, and `SECRETS.TECHEX_TXCORE_API_KEY_PROD`) are properly configured in the environment.\n\n## Author\n[<NAME_EMAIL>](mailto:<EMAIL>)\n\n## qibb Docs\n[📚 qibb Platform Documentation](https://docs.qibb.com/platform/latest/)"}, {"id": "1db06beb1dcc91fe", "type": "http request", "z": "77bf3dd067e362fd", "g": "8bd382fb5761ed8e", "name": "Call API", "method": "use", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "fc0c0aa4939d8c8d", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 3385, "y": 840, "wires": [["ad3959344fc6bdc7"]], "l": false, "info": "# Flow Format API Request\n\n## Description\nThis function node formats an API request to switch the stream status to either **Main** or **Backup**. It dynamically constructs the API endpoint, sets the HTTP method, headers, and payload, and attaches context information to the message object.\n\n## Nodes\n| Node ID | Node Type | Node Name |\n|---------|-----------|------------|\n| format_request | function | Format API Request |\n| http_request | http request | Call API |\n| fc0c0aa4939d8c8d | tls-config | (TLS Config) |\n\n## Input\n- **Trigger:** Receives a message with the following property:\n  - `msg.payload`: A string indicating the desired stream type (`'main'` or `'backup'`).\n- **Context / Global Vars:**\n  - `SECRETS.TECHEX_TXCORE_URL_PROD`: Base URL for the TXCore API.\n  - `SECRETS.TECHEX_TXEDGE_MWEDGE_ID_PROD`: MWEdge ID.\n  - `SECRETS.TECHEX_TXEDGE_BACKUP_SOURCE_ID`: Backup source ID.\n  - `SECRETS.TECHEX_TXEDGE_MAIN_SOURCE_ID`: Main source ID.\n  - `SECRETS.TECHEX_TXCORE_API_KEY_PROD`: Bearer API key.\n\n## Output\n- **Success Path:**\n  - Updates the `msg` object with the following properties:\n    - `msg.url`: The API endpoint URL for the selected stream type.\n    - `msg.method`: HTTP method set to `PUT`.\n    - `msg.headers`: Includes `Accept` and `Authorization` headers.\n    - `msg.payload`: JSON payload to activate the stream.\n    - `msg.originalStream`: The original stream type for context.\n  - Passes the updated `msg` object to the `http_request` node.\n\n## Notes\n> - Ensure that all required global secrets (`SECRETS.TECHEX_TXCORE_URL_PROD`, `SECRETS.TECHEX_TXEDGE_MWEDGE_ID_PROD`, `SECRETS.TECHEX_TXEDGE_BACKUP_SOURCE_ID`, `SECRETS.TECHEX_TXEDGE_MAIN_SOURCE_ID`, and `SECRETS.TECHEX_TXCORE_API_KEY_PROD`) are properly configured in the environment.\n> - Logs the request intent for debugging purposes.\n> - The `http_request` node processes the formatted request and sends it to the API.\n\n## Author\n[<NAME_EMAIL>](mailto:<EMAIL>)\n\n## qibb Docs\n[📚 qibb Platform Documentation](https://docs.qibb.com/platform/latest/)\n"}, {"id": "638f99ce9d7d113f", "type": "debug", "z": "77bf3dd067e362fd", "g": "8bd382fb5761ed8e", "name": "Debug OK: TechEx API Call", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 3465, "y": 780, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "ad3959344fc6bdc7", "type": "switch", "z": "77bf3dd067e362fd", "g": "8bd382fb5761ed8e", "name": "Check StatusCode", "property": "statusCode", "propertyType": "msg", "rules": [{"t": "eq", "v": "200", "vt": "str"}, {"t": "neq", "v": "200", "vt": "str"}], "checkall": "true", "repair": false, "outputs": 2, "x": 3465, "y": 840, "wires": [["638f99ce9d7d113f", "16a44882495a176e"], ["dab587d380a58a3e"]], "l": false}, {"id": "dab587d380a58a3e", "type": "debug", "z": "77bf3dd067e362fd", "g": "8bd382fb5761ed8e", "name": "Debug Error: TechEx API Call", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 3465, "y": 900, "wires": [], "icon": "node-red/alert.svg", "l": false, "info": "# Flow Handle API Response\n\n## Description\nThis function node processes the API response for a stream switch request, handling both success and error scenarios. It formats the response into a structured payload and routes it to the appropriate nodes for further processing.\n\n## Nodes\n| Node ID | Node Type | Node Name |\n|---------|-----------|------------|\n| handle_response | function | Handle API Response |\n| 1222e9b978a93ef8 | link out | to System Control |\n| a7cfa669a61a5823 | debug | Debug Error: TEXCORE Get MWEdge |\n\n## Input\n- **Trigger:**\n  - Receives a message from the `http_request` node containing the API response.\n- **Context / Global Vars:**\n  - None required.\n- **Message Properties:**\n  - `msg.statusCode`: The HTTP status code returned by the API.\n  - `msg.payload`: The response payload from the API.\n  - `msg.originalStream`: The original stream type (`'main'` or `'backup'`) for context.\n\n## Output\n- **Success Path:**\n  - If `msg.statusCode === 200`, the node:\n    - Logs a success message.\n    - Updates `msg.payload` with:\n      - `success`: `true`\n      - `stream`: The name of the stream from the API response.\n      - `originalStream`: The original stream type.\n      - `data`: The full API response payload.\n      - `message`: A success message.\n      - `timestamp`: The current timestamp.\n    - Sets `msg.topic` to `\"success\"`.\n    - Routes the message to connected nodes (`notification_template`, `to System Control`, and `Debug Error: TEXCORE Get MWEdge`).\n\n- **Error Path:**\n  - If `msg.statusCode !== 200`, the node:\n    - Logs an error message.\n    - Updates `msg.payload` with:\n      - `success`: `false`\n      - `stream`: The name of the stream (or `\"unknown\"` if not available).\n      - `originalStream`: The original stream type.\n      - `error`: A descriptive error message.\n      - `rawResponse`: The full API response payload.\n      - `timestamp`: The current timestamp.\n    - Sets `msg.topic` to `\"error\"`.\n    - Routes the message to connected nodes for error handling.\n\n## Notes\n> - Ensure that the API response structure includes a `name` field in the payload for the stream name. If not, the stream name will default to `\"unknown\"`.\n> - The `Debug Error: TEXCORE Get MWEdge` node is used to log and inspect errors during development or troubleshooting.\n> - The `link out` node (`to System Control`) routes the response back to the UI for updating the button state.\n\n## Author\n[<NAME_EMAIL>](mailto:<EMAIL>)\n\n## qibb Docs\n[📚 qibb Platform Documentation](https://docs.qibb.com/platform/latest/)\n"}, {"id": "16a44882495a176e", "type": "function", "z": "77bf3dd067e362fd", "g": "8bd382fb5761ed8e", "name": "Handle API Response", "func": "const statusCode = msg.statusCode || 0;\nconst time = new Date().toISOString();\n\n// Error path: If the API call fails\nif (statusCode !== 200) {\n    const errorPayload = {\n        success: false,\n        action: `Switch to ${msg.currentSource}`,\n        error: `API call failed with status ${statusCode}`,\n        details: `Response: ${JSON.stringify(msg.payload)}`,\n        timestamp: time\n    };\n    \n    // Remove unnecessary properties to clean up the message\n    delete msg.payload;\n    delete msg.headers;\n    delete msg.url;\n    delete msg.method;\n    delete msg.redirectList;\n    delete msg.retry;\n    delete msg.responseCookies;\n\n    msg.payload = errorPayload;\n    msg.topic = \"error\";\n\n    node.error(`[ERROR] API returned ${statusCode}: ${JSON.stringify(msg.payload)}`, msg);\n    node.send([null, msg]); // Send to the second output for errors\n    return;\n}\n\n// Success path: If the API call is successful\nconst newSource = msg.currentSource;\nconst oldSource = msg.change.split(' ')[3];\n\nconst successPayload = {\n    success: true,\n    action: `Switched from ${oldSource} to ${newSource}`,\n    message: `Source successfully switched to '${newSource}'`,\n    timestamp: time\n};\n\n// Remove unnecessary properties to clean up the message\ndelete msg.payload;\ndelete msg.headers;\ndelete msg.url;\ndelete msg.method;\ndelete msg.redirectList;\ndelete msg.retry;\ndelete msg.responseCookies;\n\nmsg.payload = successPayload;\nmsg.topic = \"success\";\n\nnode.log(`[SUCCESS] Source switched from '${oldSource}' to '${newSource}'`);\nnode.send([msg, null]); // Send to the first output for success\nreturn;\n", "outputs": 2, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 3555, "y": 840, "wires": [["09f66accaa737dab", "73ebbea621e63b03"], ["b66e0788b5142317"]], "l": false}, {"id": "09f66accaa737dab", "type": "debug", "z": "77bf3dd067e362fd", "g": "8bd382fb5761ed8e", "name": "Debug OK: Switch Topic", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 3565, "y": 780, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "b66e0788b5142317", "type": "debug", "z": "77bf3dd067e362fd", "g": "8bd382fb5761ed8e", "name": "Debug Error: TechEx API Call", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 3565, "y": 900, "wires": [], "icon": "node-red/alert.svg", "l": false, "info": "# Flow Handle API Response\n\n## Description\nThis function node processes the API response for a stream switch request, handling both success and error scenarios. It formats the response into a structured payload and routes it to the appropriate nodes for further processing.\n\n## Nodes\n| Node ID | Node Type | Node Name |\n|---------|-----------|------------|\n| handle_response | function | Handle API Response |\n| 1222e9b978a93ef8 | link out | to System Control |\n| a7cfa669a61a5823 | debug | Debug Error: TEXCORE Get MWEdge |\n\n## Input\n- **Trigger:**\n  - Receives a message from the `http_request` node containing the API response.\n- **Context / Global Vars:**\n  - None required.\n- **Message Properties:**\n  - `msg.statusCode`: The HTTP status code returned by the API.\n  - `msg.payload`: The response payload from the API.\n  - `msg.originalStream`: The original stream type (`'main'` or `'backup'`) for context.\n\n## Output\n- **Success Path:**\n  - If `msg.statusCode === 200`, the node:\n    - Logs a success message.\n    - Updates `msg.payload` with:\n      - `success`: `true`\n      - `stream`: The name of the stream from the API response.\n      - `originalStream`: The original stream type.\n      - `data`: The full API response payload.\n      - `message`: A success message.\n      - `timestamp`: The current timestamp.\n    - Sets `msg.topic` to `\"success\"`.\n    - Routes the message to connected nodes (`notification_template`, `to System Control`, and `Debug Error: TEXCORE Get MWEdge`).\n\n- **Error Path:**\n  - If `msg.statusCode !== 200`, the node:\n    - Logs an error message.\n    - Updates `msg.payload` with:\n      - `success`: `false`\n      - `stream`: The name of the stream (or `\"unknown\"` if not available).\n      - `originalStream`: The original stream type.\n      - `error`: A descriptive error message.\n      - `rawResponse`: The full API response payload.\n      - `timestamp`: The current timestamp.\n    - Sets `msg.topic` to `\"error\"`.\n    - Routes the message to connected nodes for error handling.\n\n## Notes\n> - Ensure that the API response structure includes a `name` field in the payload for the stream name. If not, the stream name will default to `\"unknown\"`.\n> - The `Debug Error: TEXCORE Get MWEdge` node is used to log and inspect errors during development or troubleshooting.\n> - The `link out` node (`to System Control`) routes the response back to the UI for updating the button state.\n\n## Author\n[<NAME_EMAIL>](mailto:<EMAIL>)\n\n## qibb Docs\n[📚 qibb Platform Documentation](https://docs.qibb.com/platform/latest/)\n"}, {"id": "73ebbea621e63b03", "type": "link out", "z": "77bf3dd067e362fd", "g": "8bd382fb5761ed8e", "name": "Restart Flow after Switch", "mode": "link", "links": ["b84d818dc7114d95"], "x": 3665, "y": 860, "wires": []}, {"id": "521db1c3df2fb0db", "type": "change", "z": "77bf3dd067e362fd", "g": "8bd382fb5761ed8e", "name": "MATCH-1-MAIN-WF-me-prod-az1", "rules": [{"t": "set", "p": "_groupId", "pt": "msg", "to": "$string($millis())", "tot": "jsonata"}, {"t": "set", "p": "techex.baseUrlProd", "pt": "msg", "to": "SECRETS.TECHEX_TXCORE_URL_PROD", "tot": "global"}, {"t": "set", "p": "techex.apiKeyProd", "pt": "msg", "to": "SECRETS.TECHEX_TXCORE_API_KEY_PROD", "tot": "global"}, {"t": "set", "p": "method", "pt": "msg", "to": "GET", "tot": "str"}, {"t": "set", "p": "techex.edgeId", "pt": "msg", "to": "688c0bf7767e0dc96809e5b2", "tot": "str"}, {"t": "set", "p": "techex.streamId", "pt": "msg", "to": "cbd839e9269828cbcfa24645055221a4393ae6e11ba82bf4", "tot": "str"}, {"t": "set", "p": "techex.outputId", "pt": "msg", "to": "8ec7fbbe412689b6bb901619b8d88d2a31a79ff748dee92b", "tot": "str"}, {"t": "set", "p": "techex.sourceIdMain", "pt": "msg", "to": "0fcf39d8eabcabed91c45e2826cb684af002a785be74cb70", "tot": "str"}, {"t": "set", "p": "techex.sourceIdBackup", "pt": "msg", "to": "ae0bb736b6e6f44246878cf73faa9f2b8f68bc2fae235906", "tot": "str"}], "x": 3150, "y": 840, "wires": [["4329be93cdce4ac1"]]}, {"id": "fc0c0aa4939d8c8d", "type": "tls-config", "name": "", "cert": "", "key": "", "ca": "", "certname": "cert.pem", "keyname": "key.pem", "caname": "", "servername": "", "verifyservercert": false, "alpnprotocol": ""}, {"id": "f26f142617d4ea7a", "type": "group", "z": "77bf3dd067e362fd", "name": "001 - Automatic Status update (10sec)", "style": {"label": true}, "nodes": ["b743a57233f66c96", "45f93df090d7f199", "2ce69e740fdc64c7", "b84d818dc7114d95"], "x": 934, "y": 719, "w": 412, "h": 202}, {"id": "b743a57233f66c96", "type": "inject", "z": "77bf3dd067e362fd", "g": "f26f142617d4ea7a", "name": "Inject Get Status", "props": [], "repeat": "10", "crontab": "", "once": true, "onceDelay": 0.1, "topic": "", "x": 995, "y": 820, "wires": [["45f93df090d7f199"]], "l": false}, {"id": "45f93df090d7f199", "type": "change", "z": "77bf3dd067e362fd", "g": "f26f142617d4ea7a", "name": "MATCH-1-MAIN-WF-me-prod-az1", "rules": [{"t": "set", "p": "_groupId", "pt": "msg", "to": "$string($millis())", "tot": "jsonata"}, {"t": "set", "p": "techex.baseUrlProd", "pt": "msg", "to": "SECRETS.TECHEX_TXCORE_URL_PROD", "tot": "global"}, {"t": "set", "p": "techex.apiKeyProd", "pt": "msg", "to": "SECRETS.TECHEX_TXCORE_API_KEY_PROD", "tot": "global"}, {"t": "set", "p": "method", "pt": "msg", "to": "GET", "tot": "str"}, {"t": "set", "p": "techex.edgeId", "pt": "msg", "to": "688c0bf7767e0dc96809e5b2", "tot": "str"}, {"t": "set", "p": "techex.streamId", "pt": "msg", "to": "cbd839e9269828cbcfa24645055221a4393ae6e11ba82bf4", "tot": "str"}, {"t": "set", "p": "techex.outputId", "pt": "msg", "to": "8ec7fbbe412689b6bb901619b8d88d2a31a79ff748dee92b", "tot": "str"}, {"t": "set", "p": "techex.sourceIdMain", "pt": "msg", "to": "0fcf39d8eabcabed91c45e2826cb684af002a785be74cb70", "tot": "str"}, {"t": "set", "p": "techex.sourceIdBackup", "pt": "msg", "to": "ae0bb736b6e6f44246878cf73faa9f2b8f68bc2fae235906", "tot": "str"}], "x": 1180, "y": 820, "wires": [["2ce69e740fdc64c7", "8142a8f81e0c3932", "b5b1693ac00c1b31", "698cb44a475df783", "440779cba7e085ee"]]}, {"id": "2ce69e740fdc64c7", "type": "debug", "z": "77bf3dd067e362fd", "g": "f26f142617d4ea7a", "name": "Debug OK: Configuration", "active": false, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "techex.edgeId", "statusType": "msg", "x": 1055, "y": 760, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "b84d818dc7114d95", "type": "link in", "z": "77bf3dd067e362fd", "g": "f26f142617d4ea7a", "name": "link in 4", "links": ["73ebbea621e63b03", "d3e9c57c92c10bce"], "x": 995, "y": 880, "wires": [["45f93df090d7f199"]]}]