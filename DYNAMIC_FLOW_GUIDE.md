# Dynamic Node-RED Flow Implementation Guide

## Overview
This document explains the new dynamic Node-RED flow that replaces your static 2-source architecture with a configurable system supporting 2-3+ sources.

## Key Files
- `dynamic-flow.json` - Complete Node-RED flow implementation
- `DYNAMIC_FLOW_GUIDE.md` - This guide

## Architecture Changes

### From Static to Dynamic

**Before (Static):**
- Separate groups for Main/Backup sources
- Fixed join count of 4
- Hardcoded source IDs
- Static UI with 2 buttons

**After (Dynamic):**
- Single configuration-driven orchestrator
- Dynamic join count based on config
- Configurable source definitions
- Dynamic UI generation

## Configuration Structure

The flow uses this configuration object stored in flow context:

```json
{
    "edgeId": "6885dadf539d77930a6e3f8a",
    "edgeName": "MATCH-1-BACKUP-WF-eu-cont-az2",
    "sources": [
        {
            "sourceId": "2fb6f4fca6f9af1b48b35d8adc835002bcec7679cfbe6b7f",
            "sourceName": "MATCH-1-BACKUP-WF-SPL-eu-cont-az2-Input A",
            "type": "Main"
        },
        {
            "sourceId": "93d4edffa63c83a188312c1e267286b0078e9ad1fa35f6ac",
            "sourceName": "MATCH-1-BACKUP-WF-eu-cont-az2-Input Caller",
            "type": "Backup"
        }
    ],
    "outputs": [
        {
            "outputId": "d045eb67e21e173a49622e96b8af30deac7cfc147d11a9c3",
            "outputName": "MATCH-1-BACKUP-WF-SPL-eu-cont-az2-Output"
        }
    ],
    "streams": [
        {
            "streamId": "b7e0b573afaaf311ae6a6364be4a15bc70d8d2eb2e256ab5",
            "streamName": "MATCH-1-BACKUP-WF-SPL-eu-cont-az2"
        }
    ]
}
```

## Flow Components (Organized in Logical Groups)

### Group [001] - Configuration & Timer
- **Configuration Store** (`config_node_001`) - Stores edge configuration in flow context
- **10-Second Sync Timer** (`inject_timer_001`) - Triggers automatic status updates

### Group [002] - Dynamic API Orchestration
- **Dynamic API Orchestrator** (`orchestrator_001`) - Generates API requests from configuration
- **Delay Router** (`delay_router_001`) - Routes messages based on timing requirements
- **Delay Nodes** (`delay_1s_001`, `delay_2s_001`, `delay_3s_001`) - Staggered timing

### Group [003] - API Processing & HTTP Requests
- **Dynamic API Processor** (`api_processor_001`) - Handles all endpoint types with rate limiting
- **API Request** (`http_request_001`) - Executes HTTP calls
- **Clean Headers** (`header_cleanup_001`) - Removes temporary headers

### Group [004] - Response Handling & Join
- **HTTP → Join Wrapper** (`http_wrapper_001`) - Wraps responses for join processing
- **Dynamic Join** (`dynamic_join_001`) - Collects all API responses
- **Gate & Retry on 429** (`gate_retry_001`) - Handles rate limiting and errors
- **Retry Logic** (`retry_handler_001`, `retry_delay_001`, `retry_cleanup_001`) - 10-second retry mechanism

### Group [005] - State Management & Mapping
- **Map → Dynamic State Model** (`state_mapper_001`) - Converts API responses to UI model
- **Detect Source Change** (`change_detector_001`) - Tracks state changes for switching

### Group [006] - User Interface
- **Dynamic Source Control UI** (`ui_template_001`) - Responsive UI with dynamic buttons
- **Handle UI Input** (`user_input_handler_001`) - Processes user interactions

### Group [007] - Source Switching Logic
- **Format Switch API Request** (`switch_handler_001`) - Prepares switching API calls
- **Switch API Call** (`switch_http_001`) - Executes source switching
- **Handle Switch Response** (`switch_response_001`) - Processes switching results

### Group [008] - Debug & Error Handling
- **Debug: API Setup Error** (`debug_error_001`) - API configuration errors
- **Debug: HTTP Error** (`debug_http_error_001`) - HTTP request errors
- **Debug: Switch Success** (`debug_switch_success_001`) - Successful switches
- **Debug: Switch Error** (`debug_switch_error_001`) - Switch failures

## Preserved Functionality

✅ **10-Second Sync Timer** - Automatic status updates every 10 seconds
✅ **Rate Limiting** - 10ms intervals using flow context storage  
✅ **Error Handling** - 429 retry with 10-second delay, error side-outputs
✅ **State Detection** - Track source changes and trigger API calls
✅ **Header Cleanup** - Delete method/headers after HTTP requests
✅ **Staggered Delays** - 1s, 2s, 3s delays between endpoint requests
✅ **Authentication** - Same global secrets and header structures
✅ **API Patterns** - Identical URL patterns and request structures
✅ **UI Styling** - Preserved visual design and lock/unlock behavior

## Adding New Sources

To add a third source, simply modify the configuration:

```json
{
    "sources": [
        {
            "sourceId": "existing_main_source_id",
            "sourceName": "Main Source",
            "type": "Main"
        },
        {
            "sourceId": "existing_backup_source_id", 
            "sourceName": "Backup Source",
            "type": "Backup"
        },
        {
            "sourceId": "new_tertiary_source_id",
            "sourceName": "Tertiary Source", 
            "type": "Tertiary"
        }
    ]
}
```

The flow will automatically:
- Generate API requests for all 3 sources
- Adjust join count to 5 (1 stream + 3 sources + 1 output)
- Create 3 UI buttons dynamically
- Handle switching between all sources

## Installation Instructions

1. **Import Flow:**
   - Copy contents of `dynamic-flow.json`
   - Import into Node-RED
   - Deploy the flow

2. **Configure Secrets:**
   Ensure these global variables are set:
   - `SECRETS.TECHEX_TXCORE_URL_PROD`
   - `SECRETS.TECHEX_TXCORE_API_KEY_PROD`

3. **Update Configuration:**
   - Modify the configuration object in `config_node_001`
   - Update `edgeId`, source IDs, stream IDs, output IDs
   - Deploy changes

4. **Access UI:**
   - Navigate to Node-RED Dashboard
   - Find "Source Management" tab
   - Use the dynamic source control interface

## Testing

1. **Verify 10-Second Sync:**
   - Watch debug output for regular API calls
   - Confirm all endpoints are being called

2. **Test Source Switching:**
   - Unlock the UI (click lock icon)
   - Click different source buttons
   - Verify API calls in debug output

3. **Check Error Handling:**
   - Temporarily break authentication
   - Verify 429 retry logic works
   - Confirm error side-outputs function

## Troubleshooting

**Issue: No UI buttons appear**
- Check configuration object in flow context
- Verify sources array is properly formatted

**Issue: API calls failing**
- Verify global secrets are set correctly
- Check edgeId and source IDs in configuration

**Issue: Join not completing**
- Check debug output for missing responses
- Verify all endpoints are accessible

**Issue: Switch not working**
- Ensure UI is unlocked
- Check user input handler debug output
- Verify switch API response

## Memory Optimization

The new flow includes several memory leak prevention measures:
- Message cleanup after processing
- Limited flow context storage
- Automatic retry cleanup
- Efficient message routing

## Enhanced UI Features

### 🔄 **Switch Now Button**
- **Quick Cycling:** Single button to cycle through all available sources
- **Loading State:** Shows spinner and "Switching..." text during API calls
- **Smart Cycling:** Automatically moves to next source in sequence
- **Lock Integration:** Respects lock/unlock state

### 📱 **Dual Selection Methods**
- **Individual Buttons:** Direct selection of specific sources
- **Dropdown Menu:** Alternative selection method
- **Consistent Behavior:** Both methods trigger immediate switching
- **Visual Feedback:** Active source highlighting

### ⏰ **Last Updated Timestamp**
- **API Response Tracking:** Shows when data was last received from API
- **Real-time Updates:** Updates automatically on each sync cycle
- **No Live Clock:** Replaced constantly updating time with meaningful timestamp

### 🔄 **Automatic Refresh**
- **Post-Switch Refresh:** Automatically triggers fresh data after successful switch
- **Immediate UI Update:** Shows new state without waiting for next sync cycle
- **Error Handling:** Graceful handling of failed switches

### 📊 **Real Source Names**
- **API Data Display:** Shows actual source names from API responses
- **Long Name Support:** Word wrapping for lengthy source names
- **Fallback Display:** Graceful fallback to type names if API names unavailable

## Performance Benefits

- **Reduced Node Count:** Single orchestrator vs multiple groups
- **Dynamic Scaling:** Easy addition of sources without flow changes
- **Efficient Processing:** Shared HTTP processing logic
- **Maintainable Code:** Configuration-driven behavior
- **Organized Structure:** 8 logical groups for easy navigation and maintenance
- **Visual Clarity:** Color-coded groups with descriptive names following [XXX] convention
- **Enhanced UX:** Loading states and immediate feedback

## Group Organization Benefits

The flow is now organized into 8 logical groups with color-coding:
- 🟢 **[001] Configuration & Timer** - Green (foundational)
- 🔵 **[002] Dynamic API Orchestration** - Blue (orchestration)
- 🟠 **[003] API Processing & HTTP** - Orange (processing)
- 🟣 **[004] Response Handling & Join** - Purple (aggregation)
- 🟡 **[005] State Management** - Yellow (state)
- 🟢 **[006] User Interface** - Green (interface)
- 🟡 **[007] Source Switching** - Yellow (actions)
- 🔴 **[008] Debug & Error Handling** - Red (debugging)

This organization makes it easy for developers to:
- Quickly locate specific functionality
- Understand the data flow sequence
- Maintain and modify individual components
- Debug issues in specific areas
- Collaborate effectively on different sections

## UI Enhancement Details

### Enhanced Template File
- **`enhanced-ui-template.html`** - Complete UI template with all enhancements
- **Copy-Paste Ready:** Can be directly copied into the UI template node format field

### Key UI Improvements
1. **Switch Now Button:** Cycles through sources automatically
2. **Loading States:** Visual feedback during API operations
3. **Last Updated:** Meaningful timestamp instead of live clock
4. **Real Names:** Displays actual source names from API
5. **Immediate Refresh:** Auto-refresh after successful operations
6. **Enhanced Styling:** Better responsive design and loading animations
