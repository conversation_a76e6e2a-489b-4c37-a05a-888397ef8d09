# Dynamic Node-RED Flow Implementation Guide

## Overview
This document explains the new dynamic Node-RED flow that replaces your static 2-source architecture with a configurable system supporting 2-3+ sources.

## Key Files
- `dynamic-flow.json` - Complete Node-RED flow implementation
- `DYNAMIC_FLOW_GUIDE.md` - This guide

## Architecture Changes

### From Static to Dynamic

**Before (Static):**
- Separate groups for Main/Backup sources
- Fixed join count of 4
- Hardcoded source IDs
- Static UI with 2 buttons

**After (Dynamic):**
- Single configuration-driven orchestrator
- Dynamic join count based on config
- Configurable source definitions
- Dynamic UI generation

## Configuration Structure

The flow uses this configuration object stored in flow context:

```json
{
    "edgeId": "6885dadf539d77930a6e3f8a",
    "edgeName": "MATCH-1-BACKUP-WF-eu-cont-az2",
    "sources": [
        {
            "sourceId": "2fb6f4fca6f9af1b48b35d8adc835002bcec7679cfbe6b7f",
            "sourceName": "MATCH-1-BACKUP-WF-SPL-eu-cont-az2-Input A",
            "type": "Main"
        },
        {
            "sourceId": "93d4edffa63c83a188312c1e267286b0078e9ad1fa35f6ac",
            "sourceName": "MATCH-1-BACKUP-WF-eu-cont-az2-Input Caller",
            "type": "Backup"
        }
    ],
    "outputs": [
        {
            "outputId": "d045eb67e21e173a49622e96b8af30deac7cfc147d11a9c3",
            "outputName": "MATCH-1-BACKUP-WF-SPL-eu-cont-az2-Output"
        }
    ],
    "streams": [
        {
            "streamId": "b7e0b573afaaf311ae6a6364be4a15bc70d8d2eb2e256ab5",
            "streamName": "MATCH-1-BACKUP-WF-SPL-eu-cont-az2"
        }
    ]
}
```

## Flow Components

### 1. Configuration Store (`config_node_001`)
- Stores the edge configuration in flow context
- Easily modifiable for different deployments
- Triggered by the 10-second timer

### 2. Dynamic API Orchestrator (`orchestrator_001`)
- Reads configuration and generates API requests
- Creates messages for streams, sources, and outputs
- Calculates dynamic join count
- Assigns staggered delays

### 3. Delay Router (`delay_router_001`)
- Routes messages based on delay requirements
- Maintains original timing patterns (0s, 1s, 2s, 3s)

### 4. Dynamic API Processor (`api_processor_001`)
- Single function handling all endpoint types
- Preserves rate limiting (10ms intervals)
- Maintains identical authentication patterns
- Dynamic URL construction based on topic

### 5. HTTP → Join Wrapper (`http_wrapper_001`)
- Preserves original error handling patterns
- Maintains side-output for errors
- Identical response wrapping logic

### 6. Dynamic Join (`dynamic_join_001`)
- Automatically adjusts count based on configuration
- Uses parts-based joining like original
- Timeout handling preserved

### 7. State Mapper (`state_mapper_001`)
- Maps responses to dynamic UI model
- Preserves source state detection
- Handles variable source counts

### 8. UI Template (`ui_template_001`)
- Dynamic button generation
- Preserves original styling
- Lock/unlock functionality maintained
- Responsive to source count changes

## Preserved Functionality

✅ **10-Second Sync Timer** - Automatic status updates every 10 seconds
✅ **Rate Limiting** - 10ms intervals using flow context storage  
✅ **Error Handling** - 429 retry with 10-second delay, error side-outputs
✅ **State Detection** - Track source changes and trigger API calls
✅ **Header Cleanup** - Delete method/headers after HTTP requests
✅ **Staggered Delays** - 1s, 2s, 3s delays between endpoint requests
✅ **Authentication** - Same global secrets and header structures
✅ **API Patterns** - Identical URL patterns and request structures
✅ **UI Styling** - Preserved visual design and lock/unlock behavior

## Adding New Sources

To add a third source, simply modify the configuration:

```json
{
    "sources": [
        {
            "sourceId": "existing_main_source_id",
            "sourceName": "Main Source",
            "type": "Main"
        },
        {
            "sourceId": "existing_backup_source_id", 
            "sourceName": "Backup Source",
            "type": "Backup"
        },
        {
            "sourceId": "new_tertiary_source_id",
            "sourceName": "Tertiary Source", 
            "type": "Tertiary"
        }
    ]
}
```

The flow will automatically:
- Generate API requests for all 3 sources
- Adjust join count to 5 (1 stream + 3 sources + 1 output)
- Create 3 UI buttons dynamically
- Handle switching between all sources

## Installation Instructions

1. **Import Flow:**
   - Copy contents of `dynamic-flow.json`
   - Import into Node-RED
   - Deploy the flow

2. **Configure Secrets:**
   Ensure these global variables are set:
   - `SECRETS.TECHEX_TXCORE_URL_PROD`
   - `SECRETS.TECHEX_TXCORE_API_KEY_PROD`

3. **Update Configuration:**
   - Modify the configuration object in `config_node_001`
   - Update `edgeId`, source IDs, stream IDs, output IDs
   - Deploy changes

4. **Access UI:**
   - Navigate to Node-RED Dashboard
   - Find "Source Management" tab
   - Use the dynamic source control interface

## Testing

1. **Verify 10-Second Sync:**
   - Watch debug output for regular API calls
   - Confirm all endpoints are being called

2. **Test Source Switching:**
   - Unlock the UI (click lock icon)
   - Click different source buttons
   - Verify API calls in debug output

3. **Check Error Handling:**
   - Temporarily break authentication
   - Verify 429 retry logic works
   - Confirm error side-outputs function

## Troubleshooting

**Issue: No UI buttons appear**
- Check configuration object in flow context
- Verify sources array is properly formatted

**Issue: API calls failing**
- Verify global secrets are set correctly
- Check edgeId and source IDs in configuration

**Issue: Join not completing**
- Check debug output for missing responses
- Verify all endpoints are accessible

**Issue: Switch not working**
- Ensure UI is unlocked
- Check user input handler debug output
- Verify switch API response

## Memory Optimization

The new flow includes several memory leak prevention measures:
- Message cleanup after processing
- Limited flow context storage
- Automatic retry cleanup
- Efficient message routing

## Performance Benefits

- **Reduced Node Count:** Single orchestrator vs multiple groups
- **Dynamic Scaling:** Easy addition of sources without flow changes
- **Efficient Processing:** Shared HTTP processing logic
- **Maintainable Code:** Configuration-driven behavior
